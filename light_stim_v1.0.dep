<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>2</fileVersion>
    <fileChecksum>1075515836</fileChecksum>
    <configuration>
        <name>Debug</name>
        <outputs>
            <file>$TOOLKIT_DIR$\config\linker\lnk430fr2433.xcl</file>
            <file>$TOOLKIT_DIR$\inc\msp430FR2433.h</file>
            <file>$PROJ_DIR$\Debug\Obj\main.pbi</file>
            <file>$PROJ_DIR$\AD.c</file>
            <file>$PROJ_DIR$\ir.c</file>
            <file>$PROJ_DIR$\main.c</file>
            <file>$PROJ_DIR$\Debug\Exe\light_stim_v1.0.d43</file>
            <file>$PROJ_DIR$\Debug\Obj\main.r43</file>
            <file>$TOOLKIT_DIR$\inc\in430.h</file>
            <file>$PROJ_DIR$\Debug\Obj\AD.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\AD.r43</file>
            <file>$PROJ_DIR$\Debug\Obj\ir.r43</file>
            <file>$PROJ_DIR$\Debug\Obj\main.__cstat.et</file>
            <file>$PROJ_DIR$\Debug\List\light_stim_v1.0.map</file>
            <file>$PROJ_DIR$\main.h</file>
            <file>$PROJ_DIR$\AD.h</file>
            <file>$TOOLKIT_DIR$\inc\intrinsics.h</file>
            <file>$TOOLKIT_DIR$\lib\dlib\dl430xlsfn.r43</file>
            <file>$PROJ_DIR$\ir.h</file>
            <file>$TOOLKIT_DIR$\config\linker\multiplier32.xcl</file>
            <file>$PROJ_DIR$\Debug\Obj\ir.pbi</file>
            <file>$PROJ_DIR$\Debug\Obj\light_stim_v1.pbd</file>
            <file>$PROJ_DIR$\Debug\Exe\light_stim_v1.0.ulp</file>
        </outputs>
        <file>
            <name>[ROOT_NODE]</name>
            <outputs>
                <tool>
                    <name>XLINK</name>
                    <file> 13 6</file>
                </tool>
                <tool>
                    <name>ULP430</name>
                    <file> 22</file>
                </tool>
            </outputs>
        </file>
        <file>
            <name>$PROJ_DIR$\AD.c</name>
            <outputs>
                <tool>
                    <name>ICC430</name>
                    <file> 10</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 9</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICC430</name>
                    <file> 15 1 8 16 14 18</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 14 8 1 18 15 16</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\ir.c</name>
            <outputs>
                <tool>
                    <name>ICC430</name>
                    <file> 11</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 20</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICC430</name>
                    <file> 18 1 8 16 14 15</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 1 15 8 16 14 18</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\main.c</name>
            <outputs>
                <tool>
                    <name>ICC430</name>
                    <file> 7</file>
                </tool>
                <tool>
                    <name>__cstat</name>
                    <file> 12</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 2</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>ICC430</name>
                    <file> 14 1 8 16 18 15</file>
                </tool>
                <tool>
                    <name>BICOMP</name>
                    <file> 8 18 1 14 15 16</file>
                </tool>
            </inputs>
        </file>
        <file>
            <name>$PROJ_DIR$\Debug\Exe\light_stim_v1.0.d43</name>
            <outputs>
                <tool>
                    <name>XLINK</name>
                    <file> 13</file>
                </tool>
            </outputs>
            <inputs>
                <tool>
                    <name>XLINK</name>
                    <file> 0 19 10 11 7 17</file>
                </tool>
            </inputs>
        </file>
    </configuration>
    <configuration>
        <name>Release</name>
        <outputs />
        <forcedrebuild>
            <name>[MULTI_TOOL]</name>
            <tool>XLINK</tool>
        </forcedrebuild>
    </configuration>
</project>
