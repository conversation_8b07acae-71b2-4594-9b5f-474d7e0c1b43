<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>MSP430</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>General</name>
            <archiveVersion>21</archiveVersion>
            <data>
                <version>34</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OGCore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>Debug\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Debug\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Debug\List</state>
                </option>
                <option>
                    <name>Hardware Multiplier</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AssemblerOnly</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGDouble</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>Use the normal configuration of the C/EC++ runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>RTConfigPath</name>
                    <state>$TOOLKIT_DIR$\lib\dlib\dl430xlsfn.h</state>
                </option>
                <option>
                    <name>RTLibraryPath</name>
                    <state>$TOOLKIT_DIR$\lib\dlib\dl430xlsfn.r43</state>
                </option>
                <option>
                    <name>Input variant</name>
                    <version>2</version>
                    <state>3</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>No specifier n, no float or long long.</state>
                </option>
                <option>
                    <name>Output variant</name>
                    <version>2</version>
                    <state>3</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>No specifier a or A.</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GeneralEnableMisra</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVerbose</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGChipSelectMenu</name>
                    <state>MSP430FR2433	MSP430FR2433</state>
                </option>
                <option>
                    <name>GStackHeapOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GStackSize2</name>
                    <state>160</state>
                </option>
                <option>
                    <name>GHeapSize2</name>
                    <state>160</state>
                </option>
                <option>
                    <name>RadioDataModelType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GHeap20Size</name>
                    <state>80</state>
                </option>
                <option>
                    <name>GeneralMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>RadioHeapSizeType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioHardwareMultiplierType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>RadioL092ModelType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Ropi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>NoRwDynamicInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MathLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioCodeModelType</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEnableMpu</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GESupportMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GELockMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GENMIViolationMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEAssertMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEInfoReadMpu</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEInfoWriteMpu</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEInfoExecuteMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEInfoAssertMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEnableIpe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GESupportIpe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GAssertIpe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GLockIpe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Math variant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>Math description</name>
                    <state>Default variants of cos, sin, tan, log, log10, pow, and exp.</state>
                </option>
                <option>
                    <name>GESupportFrwp</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GInfoFrwp</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GProgramFrwp</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GPersistentFrwp</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICC430</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>38</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CCDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>IObjPrefix2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>00000</state>
                </option>
                <option>
                    <name>CCObjUseModuleName</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCObjModuleName</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCharIs</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCExt</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCMigrationPreprocExtentions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IDoubleSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.r43</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCCR4Utilize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCCR5Utilize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCOverrideModuleTypeDefault</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCRadioModuleType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCRadioModuleTypeSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>newCCIncludePaths</name>
                    <state></state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CompilerMisraOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OI430X</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ReduceStack</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Save20bit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CompilerDataModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CompilerMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>CompilerMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCppDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCPUTAG</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCCodeFunctions</name>
                    <state>CODE</state>
                </option>
                <option>
                    <name>CCData16</name>
                    <state>DATA16</state>
                </option>
                <option>
                    <name>CCData20</name>
                    <state>DATA20</state>
                </option>
                <option>
                    <name>CCIntvec</name>
                    <state>INTVEC</state>
                </option>
                <option>
                    <name>CCCstack</name>
                    <state>CSTACK</state>
                </option>
                <option>
                    <name>CCRamFuncCode</name>
                    <state>RAMFUNC_CODE</state>
                </option>
                <option>
                    <name>CCIsrCode</name>
                    <state>ISR_CODE</state>
                </option>
                <option>
                    <name>CCDifunct</name>
                    <state>DIFUNCT</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CROPI</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CNoRwDynamicInit</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state></state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCGuardCallsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CompilerCodeModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCCode16Functions</name>
                    <state>CODE16</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>A430</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>14</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ADebugType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AMaxErrOn</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AMaxErrNum</name>
                    <state>100</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.r43</state>
                </option>
                <option>
                    <name>AMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>OA1M</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AStdIncludes</name>
                    <state>$TOOLKIT_DIR$\INC\</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state></state>
                </option>
                <option>
                    <name>ACPUTAG</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
            </data>
        </settings>
        <settings>
            <name>BICOMP</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>XLINK</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>30</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>XOutOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>light_stim_v1.0.d43</state>
                </option>
                <option>
                    <name>OutputFormat</name>
                    <version>11</version>
                    <state>33</state>
                </option>
                <option>
                    <name>FormatVariant</name>
                    <version>9</version>
                    <state>2</state>
                </option>
                <option>
                    <name>SecondaryOutputFile</name>
                    <state>(None for the selected format)</state>
                </option>
                <option>
                    <name>XDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AlwaysOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OverlapWarnings</name>
                    <state>0</state>
                </option>
                <option>
                    <name>NoGlobalCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XList</name>
                    <state>1</state>
                </option>
                <option>
                    <name>SegmentMap</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ListSymbols</name>
                    <state>2</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>XIncludes</name>
                    <state>$TOOLKIT_DIR$\LIB\</state>
                </option>
                <option>
                    <name>ModuleStatus</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XclOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XclFile</name>
                    <state>$TOOLKIT_DIR$\config\linker\lnk430fr2433.xcl</state>
                </option>
                <option>
                    <name>XclFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlgo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>RangeCheckAlternatives</name>
                    <state>0</state>
                </option>
                <option>
                    <name>SuppressAllWarn</name>
                    <state>0</state>
                </option>
                <option>
                    <name>SuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>TreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>TreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>ModuleLocalSym</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>XHardwareMul</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IncludeSuppressed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ModuleSummary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkStackSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XlinkCodeModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>xcProgramEntryLabel</name>
                    <state>__program_start</state>
                </option>
                <option>
                    <name>DebugInformation</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RuntimeControl</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IoEmulation</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XcRTLibraryFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OXLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XLibraryHeap</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AllowExtraOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenerateExtraOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XExtraOutOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExtraOutputFile</name>
                    <state>light_stim_v1.0.a43</state>
                </option>
                <option>
                    <name>ExtraOutputFormat</name>
                    <version>11</version>
                    <state>23</state>
                </option>
                <option>
                    <name>ExtraFormatVariant</name>
                    <version>9</version>
                    <state>2</state>
                </option>
                <option>
                    <name>xcOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>xcProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ListOutputFormat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>BufferedTermOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>OverlaySystemMap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>RawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>RawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>RawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>XLinkMisraHandler</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>2</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>XLibraryHeap20</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>LinkMathLib</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XlinkMPU</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XlinkIPE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XlinkLogEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkLogInputFiles</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkLogModuleSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkLogPrintfScanf</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkLogSegmentSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkLogStackDepth</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkStackUsageEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkControlFiles</name>
                    <state></state>
                </option>
                <option>
                    <name>XlinkCallGraphFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkCallGraphFileName</name>
                    <state>$LIST_DIR$\$PROJ_FNAME$.call_graph.cgx</state>
                </option>
                <option>
                    <name>XlinkFRWP</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>XAR</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>XAROutOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XARInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ULP430</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CUTest</name>
                    <state>-I$TOOLKIT_DIR$\inc</state>
                    <state>-@$TOOLKIT_DIR$\bin\iar.cmd</state>
                    <state>-@$PROJ_DIR$\source.txt</state>
                    <state>-@$PROJ_DIR$\include.txt</state>
                    <state>--preinclude=$PROJ_DIR$\IAR_ULPAdvisor_Defs.h</state>
                </option>
                <option>
                    <name>ULPRules</name>
                    <version>0</version>
                    <state>1111111111111111111</state>
                </option>
                <option>
                    <name>ULPEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$PROJ_FNAME$.ulp</state>
                </option>
                <option>
                    <name>ULPStatus</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BILINK</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>MSP430</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>General</name>
            <archiveVersion>21</archiveVersion>
            <data>
                <version>34</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>OGCore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>Release\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Release\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Release\List</state>
                </option>
                <option>
                    <name>Hardware Multiplier</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AssemblerOnly</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGDouble</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>Use the normal configuration of the C/EC++ runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>RTConfigPath</name>
                    <state>$TOOLKIT_DIR$\lib\dlib\dl430fn.h</state>
                </option>
                <option>
                    <name>RTLibraryPath</name>
                    <state>$TOOLKIT_DIR$\lib\dlib\dl430fn.r43</state>
                </option>
                <option>
                    <name>Input variant</name>
                    <version>2</version>
                    <state>3</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>Full formatting.</state>
                </option>
                <option>
                    <name>Output variant</name>
                    <version>2</version>
                    <state>3</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Full formatting.</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GeneralEnableMisra</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVerbose</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGChipSelectMenu</name>
                    <state>MSP430F149	MSP430F149</state>
                </option>
                <option>
                    <name>GStackHeapOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GStackSize2</name>
                    <state>80</state>
                </option>
                <option>
                    <name>GHeapSize2</name>
                    <state>80</state>
                </option>
                <option>
                    <name>RadioDataModelType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GHeap20Size</name>
                    <state>###Uninitialized###</state>
                </option>
                <option>
                    <name>GeneralMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>RadioHeapSizeType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioHardwareMultiplierType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>RadioL092ModelType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Ropi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>NoRwDynamicInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MathLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioCodeModelType</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEnableMpu</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GESupportMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GELockMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GENMIViolationMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEAssertMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEInfoReadMpu</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEInfoWriteMpu</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEInfoExecuteMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEInfoAssertMpu</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEnableIpe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GESupportIpe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GAssertIpe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GLockIpe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Math variant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>Math description</name>
                    <state></state>
                </option>
                <option>
                    <name>GESupportFrwp</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GInfoFrwp</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GProgramFrwp</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GPersistentFrwp</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICC430</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>38</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CCDefines</name>
                    <state>NDEBUG</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>IObjPrefix2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>11111</state>
                </option>
                <option>
                    <name>CCObjUseModuleName</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCObjModuleName</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCharIs</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCExt</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCMigrationPreprocExtentions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IDoubleSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCCR4Utilize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCCR5Utilize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCOverrideModuleTypeDefault</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCRadioModuleType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCRadioModuleTypeSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>newCCIncludePaths</name>
                    <state></state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CompilerMisraOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OI430X</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ReduceStack</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Save20bit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CompilerDataModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>3</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CompilerMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>CompilerMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCppDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCPUTAG</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCCodeFunctions</name>
                    <state>CODE</state>
                </option>
                <option>
                    <name>CCData16</name>
                    <state>DATA16</state>
                </option>
                <option>
                    <name>CCData20</name>
                    <state>DATA20</state>
                </option>
                <option>
                    <name>CCIntvec</name>
                    <state>INTVEC</state>
                </option>
                <option>
                    <name>CCCstack</name>
                    <state>CSTACK</state>
                </option>
                <option>
                    <name>CCRamFuncCode</name>
                    <state>RAMFUNC_CODE</state>
                </option>
                <option>
                    <name>CCIsrCode</name>
                    <state>ISR_CODE</state>
                </option>
                <option>
                    <name>CCDifunct</name>
                    <state>DIFUNCT</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CROPI</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CNoRwDynamicInit</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state>NDEBUG</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCGuardCallsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CompilerCodeModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCCode16Functions</name>
                    <state>CODE16</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>A430</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>14</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADebugType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AMaxErrOn</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AMaxErrNum</name>
                    <state>100</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>AMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>OA1M</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AStdIncludes</name>
                    <state>$TOOLKIT_DIR$\INC\</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state></state>
                </option>
                <option>
                    <name>ACPUTAG</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
            </data>
        </settings>
        <settings>
            <name>BICOMP</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>XLINK</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>30</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>XOutOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>templproj.txt</state>
                </option>
                <option>
                    <name>OutputFormat</name>
                    <version>11</version>
                    <state>33</state>
                </option>
                <option>
                    <name>FormatVariant</name>
                    <version>9</version>
                    <state>2</state>
                </option>
                <option>
                    <name>SecondaryOutputFile</name>
                    <state>(None for the selected format)</state>
                </option>
                <option>
                    <name>XDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AlwaysOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OverlapWarnings</name>
                    <state>0</state>
                </option>
                <option>
                    <name>NoGlobalCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XList</name>
                    <state>1</state>
                </option>
                <option>
                    <name>SegmentMap</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ListSymbols</name>
                    <state>2</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>XIncludes</name>
                    <state>$TOOLKIT_DIR$\LIB\</state>
                </option>
                <option>
                    <name>ModuleStatus</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XclOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XclFile</name>
                    <state>$TOOLKIT_DIR$\CONFIG\lnk430F149.xcl</state>
                </option>
                <option>
                    <name>XclFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlgo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>RangeCheckAlternatives</name>
                    <state>0</state>
                </option>
                <option>
                    <name>SuppressAllWarn</name>
                    <state>0</state>
                </option>
                <option>
                    <name>SuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>TreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>TreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>ModuleLocalSym</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>XHardwareMul</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IncludeSuppressed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ModuleSummary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkStackSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XlinkCodeModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>xcProgramEntryLabel</name>
                    <state>__program_start</state>
                </option>
                <option>
                    <name>DebugInformation</name>
                    <state>1</state>
                </option>
                <option>
                    <name>RuntimeControl</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IoEmulation</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XcRTLibraryFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OXLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XLibraryHeap</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AllowExtraOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenerateExtraOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XExtraOutOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExtraOutputFile</name>
                    <state>templproj.a43</state>
                </option>
                <option>
                    <name>ExtraOutputFormat</name>
                    <version>11</version>
                    <state>23</state>
                </option>
                <option>
                    <name>ExtraFormatVariant</name>
                    <version>9</version>
                    <state>2</state>
                </option>
                <option>
                    <name>xcOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>xcProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ListOutputFormat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>BufferedTermOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>OverlaySystemMap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>RawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>RawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>RawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>XLinkMisraHandler</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>2</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>XLibraryHeap20</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>LinkMathLib</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XlinkMPU</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XlinkIPE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XlinkLogEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkLogInputFiles</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkLogModuleSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkLogPrintfScanf</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkLogSegmentSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkLogStackDepth</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkStackUsageEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkControlFiles</name>
                    <state></state>
                </option>
                <option>
                    <name>XlinkCallGraphFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XlinkCallGraphFileName</name>
                    <state>$LIST_DIR$\$PROJ_FNAME$.call_graph.cgx</state>
                </option>
                <option>
                    <name>XlinkFRWP</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>XAR</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>XAROutOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>XARInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ULP430</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CUTest</name>
                    <state>###Uninitialized###</state>
                </option>
                <option>
                    <name>ULPRules</name>
                    <version>0</version>
                    <state>1111111111111111111</state>
                </option>
                <option>
                    <name>ULPEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>ULPStatus</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BILINK</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <file>
        <name>$PROJ_DIR$\AD.c</name>
    </file>
    <file>
        <name>$PROJ_DIR$\ir.c</name>
    </file>
    <file>
        <name>$PROJ_DIR$\main.c</name>
    </file>
</project>
