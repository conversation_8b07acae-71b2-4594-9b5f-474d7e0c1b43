<?xml version="1.0"?>
<settings>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <InterruptLog>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </InterruptLog>
    <DataLog>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
    </DataLog>
    <Breakpoints2>
        <Count>0</Count>
    </Breakpoints2>
    <Interrupts>
        <Enabled>1</Enabled>
    </Interrupts>
    <MemoryMap>
        <Enabled>0</Enabled>
        <Base>0</Base>
        <UseAuto>0</UseAuto>
        <TypeViolation>1</TypeViolation>
        <UnspecRange>1</UnspecRange>
        <ActionState>1</ActionState>
    </MemoryMap>
    <Trace1>
        <Enabled>0</Enabled>
        <ShowSource>1</ShowSource>
    </Trace1>
    <Simulator>
        <Freq>1000000</Freq>
        <FreqHi>0</FreqHi>
    </Simulator>
    <EEM_State_Storage>
        <Buffer>010000000000000000000000</Buffer>
    </EEM_State_Storage>
    <EEM_Sequencer>
        <Buffer>000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000</Buffer>
    </EEM_Sequencer>
    <EEM_Cycle_Counter_0>
        <Counter0>0000000000000000000000000000000000000000000000000000000000000000</Counter0>
    </EEM_Cycle_Counter_0>
    <EEM_Cycle_Counter_1>
        <Counter1>0000000000000000000000000000000000000000000000000000000000000000</Counter1>
    </EEM_Cycle_Counter_1>
    <DebugChecksum>
        <Checksum>3443334483</Checksum>
    </DebugChecksum>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <Disassembly>
        <MixedMode>1</MixedMode>
    </Disassembly>
    <watch_formats>
        <Fmt0>{W}1:ADCCTL0	4	0</Fmt0>
        <Fmt1>{W}1:ADCCTL1	4	0</Fmt1>
        <Fmt2>{W}1:ADCDIV_7	4	0</Fmt2>
        <Fmt3>{W}1:ADCIV	4	0</Fmt3>
        <Fmt4>{W}1:CSCTL0	4	0</Fmt4>
        <Fmt5>{W}1:CSCTL1	4	0</Fmt5>
        <Fmt6>{W}1:CSCTL2	4	0</Fmt6>
        <Fmt7>{W}1:CSCTL3	4	0</Fmt7>
        <Fmt8>{W}1:CSCTL4	4	0</Fmt8>
        <Fmt9>{W}1:CSCTL5	4	0</Fmt9>
        <Fmt10>{W}1:CSCTL6	4	0</Fmt10>
        <Fmt11>{W}1:CSCTL7	4	0</Fmt11>
        <Fmt12>{W}1:CSCTL8	4	0</Fmt12>
        <Fmt13>{W}1:P1DIR	1	0</Fmt13>
        <Fmt14>{W}1:P1IE	1	0</Fmt14>
        <Fmt15>{W}1:P1IES	1	0</Fmt15>
        <Fmt16>{W}1:P1IFG	1	0</Fmt16>
        <Fmt17>{W}1:P1IN	1	0</Fmt17>
        <Fmt18>{W}1:P1OUT	1	0</Fmt18>
        <Fmt19>{W}1:P1REN	1	0</Fmt19>
        <Fmt20>{W}1:SYSCFG2	4	0</Fmt20>
        <Fmt21>{W}1:TA1CTL	1	0</Fmt21>
        <Fmt22>{W}1:TA1IV	1	0</Fmt22>
        <Fmt23>{W}1:ir_ifg	1	0</Fmt23>
        <Fmt24>{W}1:ir_in	1	0</Fmt24>
        <Fmt25>{W}1:ir_re_bit	4	0</Fmt25>
        <Fmt26>{W}1:test1	4	0</Fmt26>
        <Fmt27>{W}1:us_to_ms	4	0</Fmt27>
    </watch_formats>
    <LogFile>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
        <Category>_ 0</Category>
    </LogFile>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <Breakpoints>
        <Count>0</Count>
    </Breakpoints>
    <FET>
        <Clock_mode>46</Clock_mode>
        <Extended_Clock_mode>1055</Extended_Clock_mode>
        <Secure_Password />
        <Extended_Clock_Control_Enable>1</Extended_Clock_Control_Enable>
        <Advanced_Extended_Clock_Control>0</Advanced_Extended_Clock_Control>
        <Emulation_mode>0</Emulation_mode>
        <Free_running>0</Free_running>
        <Shutting_Down>3</Shutting_Down>
    </FET>
    <Memory_Dump>
        <Start_address />
        <Lenghth />
        <Address_info>0</Address_info>
        <Format>0</Format>
        <Dump_registers>0</Dump_registers>
        <PC>0</PC>
        <SP>0</SP>
        <SR>0</SR>
        <all_registers>0</all_registers>
        <File_name />
    </Memory_Dump>
    <Aliases>
        <Count>0</Count>
        <SuppressDialog>0</SuppressDialog>
    </Aliases>
</settings>
