################################################################################
#                                                                              #
#      IAR Universal Linker V6.5.1.95                                          #
#                                                                              #
#           Link time     =  30/Mar/2023  15:07:17                             #
#           Target CPU    =  MSP430                                            #
#           List file     =  "H:\project_software\yige\02_stimulation_light\ms #
#                            p403fr2433\light_stim_V1.0 - 20220926_20221104\De #
#                            bug\List\light_stim_v1.0.map"                     #
#           Output file 1 =  "H:\project_software\yige\02_stimulation_light\ms #
#                            p403fr2433\light_stim_V1.0 - 20220926_20221104\De #
#                            bug\Exe\light_stim_v1.0.d43"                      #
#                            Format: ubrof10                                   #
#                            UBROF version 10.0.6                              #
#                            Using library modules for C-SPY (-rt)             #
#           Command line  =  -f C:\Users\<USER>\AppData\Local\Temp\EW2587.tmp       #
#                            ("H:\project_software\yige\02_stimulation_light\m #
#                            sp403fr2433\light_stim_V1.0 - 20220926_20221104\D #
#                            ebug\Obj\AD.r43"                                  #
#                            "H:\project_software\yige\02_stimulation_light\ms #
#                            p403fr2433\light_stim_V1.0 - 20220926_20221104\De #
#                            bug\Obj\ir.r43"                                   #
#                            "H:\project_software\yige\02_stimulation_light\ms #
#                            p403fr2433\light_stim_V1.0 - 20220926_20221104\De #
#                            bug\Obj\main.r43"                                 #
#                            -o                                                #
#                            "H:\project_software\yige\02_stimulation_light\ms #
#                            p403fr2433\light_stim_V1.0 - 20220926_20221104\De #
#                            bug\Exe\light_stim_v1.0.d43"                      #
#                            -l                                                #
#                            "H:\project_software\yige\02_stimulation_light\ms #
#                            p403fr2433\light_stim_V1.0 - 20220926_20221104\De #
#                            bug\List\light_stim_v1.0.map"                     #
#                            -xms                                              #
#                            "-ID:\Program Files (x86)\IAR Systems\Embedded Wo #
#                            rkbench 8.0\430\LIB\"                             #
#                            -f                                                #
#                            "D:\Program Files (x86)\IAR Systems\Embedded Work #
#                            bench 8.0\430\config\linker\lnk430fr2433.xcl"     #
#                            (-cmsp430 -QCODE_I=CODE_ID -QTLS16_I=TLS16_ID     #
#                            -D__iar_HWMUL=4C0 -Z(CONST)INFO=1800-19FF         #
#                            -Z(CONST)INFOA=1800-19FF                          #
#                            -Z(DATA)DATA16_I,DATA16_Z,DATA16_N,TLS16_I=2000-2 #
#                            FFF                                               #
#                            -Z(DATA)CODE_I                                    #
#                            -Z(DATA)DATA20_I,DATA20_Z,DATA20_N                #
#                            -Z(DATA)CSTACK+_STACK_SIZE#                       #
#                            -Z(CONST)DATA16_P,DATA20_P=C400-FF7F              #
#                            -Z(DATA)DATA16_HEAP+_DATA16_HEAP_SIZE,DATA20_HEAP #
#                            +_DATA20_HEAP_SIZE                                #
#                            -Z(CONST)DATA16_C,DATA16_ID,TLS16_ID,DIFUNCT,CHEC #
#                            KSUM=C400-FF7F                                    #
#                            -Z(CONST)DATA20_C,DATA20_ID                       #
#                            -Z(CODE)CSTART,ISR_CODE,CODE_ID=C400-FF7F         #
#                            -P(CODE)CODE,CODE16=C400-FF7F                     #
#                            -Z(CONST)SIGNATURE=FF80-FF87                      #
#                            -Z(CODE)INTVEC=FF88-FFFF                          #
#                            -Z(CODE)RESET=FFFE-FFFF) -f                       #
#                            "D:\Program Files (x86)\IAR Systems\Embedded Work #
#                            bench 8.0\430\config\linker\multiplier32.xcl"     #
#                            (-e?Mul8Hw=?Mul8 -e?Mul16Hw=?Mul16                #
#                            -e?Mul16to32uHw=?Mul16to32u                       #
#                            -e?Mul16to32sHw=?Mul16to32s -e?Mul32Hw32=?Mul32   #
#                            -e?Mul64Hw32=_Mul64i                              #
#                            -e?Mul32u32uto64iHw32=_Mul32u32uto64i             #
#                            -e?Mul32fHw32=_Mul32f                             #
#                            -e_Mul64fHw32Ptr=_Mul64fPtr) -D_STACK_SIZE=A0     #
#                            -rt                                               #
#                            "D:\Program Files (x86)\IAR Systems\Embedded Work #
#                            bench 8.0\430\lib\dlib\dl430xlsfn.r43"            #
#                            -e_PrintfLarge=_Printf -e_ScanfLarge=_Scanf       #
#                            -D_DATA16_HEAP_SIZE=A0 -s __program_start         #
#                            -D_DATA20_HEAP_SIZE=50)                           #
#                                                                              #
#                           Copyright (C) 1987-2016 IAR Systems AB.            #
################################################################################





                ****************************************
                *                                      *
                *           CROSS REFERENCE            *
                *                                      *
                ****************************************

       Program entry at :           C462  Relocatable, from module : ?cstart




                ****************************************
                *                                      *
                *            RUNTIME MODEL             *
                *                                      *
                ****************************************

  __Heap_Handler             = Basic
  __SystemLibrary            = DLib
  __code_model               = large
  __core                     = 430X
  __data_model               = small
  __dlib_file_descriptor     = 0
  __dlib_full_locale_support = 0
  __double_size              = 32
  __pic                      = no
  __reg_r4                   = free
  __reg_r5                   = free
  __rt_version               = 3

                ****************************************
                *                                      *
                *    AUTOMATIC SYMBOL REDIRECTIONS     *
                *                                      *
                ****************************************

                  There are no automatic symbol redirections.



                ****************************************
                *                                      *
                *              MODULE MAP              *
                *                                      *
                ****************************************


  DEFINED ABSOLUTE ENTRIES
    *************************************************************************

  DEFINED ABSOLUTE ENTRIES
  PROGRAM MODULE, NAME : ?ABS_ENTRY_MOD

Absolute parts
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _DATA20_HEAP_SIZE       0050 
           _DATA16_HEAP_SIZE       00A0 
           _STACK_SIZE             00A0 
           __iar_HWMUL             04C0 
    *************************************************************************

  FILE NAME : H:\project_software\yige\02_stimulation_light\msp403fr2433\light_stim_V1.0 - 20220926_20221104\Debug\Obj\AD.r43
    *************************************************************************

  FILE NAME : H:\project_software\yige\02_stimulation_light\msp403fr2433\light_stim_V1.0 - 20220926_20221104\Debug\Obj\ir.r43
  PROGRAM MODULE, NAME : ir

  SEGMENTS IN THE MODULE
  ======================
DATA16_AN
  Relative segment, address: 0202 - 0203 (0x2 bytes), align: 0
  Segment part 1. ROOT        Intra module refs:   HallSensorSetup (main)
                                                   IR_Setup
                                                   LED_gree_OFF (main)
                                                   LED_gree_ON (main)
                                                   LED_red_OFF (main)
                                                   LED_red_ON (main)
                                                   config_led_cs_io_out (main)
                                                   main (main)
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PAOUT_L              0202 
           LOCAL                   ADDRESS         
           =====                   =======         
           PAOUT                   0202 
           PAOUT_H                 0203 
           PAOUT_L                 0202 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0204 - 0205 (0x2 bytes), align: 0
  Segment part 2. ROOT        Intra module refs:   Timer_B_con (main)
                                                   config_led_cs_io_out (main)
                                                   main (main)
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PADIR_L              0204 
           LOCAL                   ADDRESS         
           =====                   =======         
           PADIR                   0204 
           PADIR_H                 0205 
           PADIR_L                 0204 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0206 - 0207 (0x2 bytes), align: 0
  Segment part 3. ROOT        Intra module refs:   HallSensorSetup (main)
                                                   IR_Setup
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PAREN_L              0206 
           LOCAL                   ADDRESS         
           =====                   =======         
           PAREN                   0206 
           PAREN_H                 0207 
           PAREN_L                 0206 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0218 - 0219 (0x2 bytes), align: 0
  Segment part 4. ROOT        Intra module refs:   HallSensorSetup (main)
                                                   IR_Setup
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PAIES_L              0218 
           LOCAL                   ADDRESS         
           =====                   =======         
           PAIES                   0218 
           PAIES_H                 0219 
           PAIES_L                 0218 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 021A - 021B (0x2 bytes), align: 0
  Segment part 5. ROOT        Intra module refs:   HallSensorSetup (main)
                                                   IR_Setup
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PAIE_L               021A 
           LOCAL                   ADDRESS         
           =====                   =======         
           PAIE                    021A 
           PAIE_H                  021B 
           PAIE_L                  021A 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 021C - 021D (0x2 bytes), align: 0
  Segment part 6. ROOT        Intra module refs:   HallSensorSetup (main)
                                                   IR_Setup
                                                   Port_1 (main)
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PAIFG_L              021C 
           LOCAL                   ADDRESS         
           =====                   =======         
           PAIFG                   021C 
           PAIFG_H                 021D 
           PAIFG_L                 021C 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CDFE - CE1D (0x20 bytes), align: 1
  Segment part 8.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_Setup                CDFE            main (main)
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CC2E - CD5F (0x132 bytes), align: 1
  Segment part 12.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_decode_reception     CC2E            main (main)

    *************************************************************************

  FILE NAME : H:\project_software\yige\02_stimulation_light\msp403fr2433\light_stim_V1.0 - 20220926_20221104\Debug\Obj\main.r43
  PROGRAM MODULE, NAME : main

  SEGMENTS IN THE MODULE
  ======================
DATA16_AN
  Relative segment, address: 0700 - 0701 (0x2 bytes), align: 0
  Segment part 1. ROOT        Intra module refs:   ADC_ISR
                                                   battery_AD_int
                                                   charge_AD_int
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_ADCCTL0_L            0700 
           LOCAL                   ADDRESS         
           =====                   =======         
           ADCCTL0                 0700 
           ADCCTL0_H               0701 
           ADCCTL0_L               0700 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0702 - 0703 (0x2 bytes), align: 0
  Segment part 2. ROOT        Intra module refs:   battery_AD_int
                                                   charge_AD_int
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_ADCCTL1_L            0702 
           LOCAL                   ADDRESS         
           =====                   =======         
           ADCCTL1                 0702 
           ADCCTL1_H               0703 
           ADCCTL1_L               0702 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 070A - 070B (0x2 bytes), align: 0
  Segment part 3. ROOT        Intra module refs:   battery_AD_int
                                                   charge_AD_int
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_ADCMCTL0_L           070A 
           LOCAL                   ADDRESS         
           =====                   =======         
           ADCMCTL0                070A 
           ADCMCTL0_H              070B 
           ADCMCTL0_L              070A 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0712 - 0713 (0x2 bytes), align: 0
  Segment part 4. ROOT        Intra module refs:   battery_AD_int
                                                   charge_AD_int
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_ADCMEM0_L            0712 
           LOCAL                   ADDRESS         
           =====                   =======         
           ADCMEM0                 0712 
           ADCMEM0_H               0713 
           ADCMEM0_L               0712 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 071A - 071B (0x2 bytes), align: 0
  Segment part 5. ROOT        Intra module refs:   battery_AD_int
                                                   charge_AD_int
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_ADCIE_L              071A 
           LOCAL                   ADDRESS         
           =====                   =======         
           ADCIE                   071A 
           ADCIE_H                 071B 
           ADCIE_L                 071A 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 071C - 071D (0x2 bytes), align: 0
  Segment part 6. ROOT        Intra module refs:   ADC_ISR
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_ADCIFG_L             071C 
           LOCAL                   ADDRESS         
           =====                   =======         
           ADCIFG                  071C 
           ADCIFG_H                071D 
           ADCIFG_L                071C 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0130 - 0131 (0x2 bytes), align: 0
  Segment part 7. ROOT        Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PM5CTL0_L            0130 
           LOCAL                   ADDRESS         
           =====                   =======         
           PM5CTL0                 0130 
           PM5CTL0_H               0131 
           PM5CTL0_L               0130 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 020A - 020B (0x2 bytes), align: 0
  Segment part 11. ROOT       Intra module refs:   Timer_B_con
                                                   config_led_cs_io_out
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PASEL0_L             020A 
           LOCAL                   ADDRESS         
           =====                   =======         
           PASEL0                  020A 
           PASEL0_H                020B 
           PASEL0_L                020A 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 020C - 020D (0x2 bytes), align: 0
  Segment part 12. ROOT       Intra module refs:   Timer_B_con
                                                   config_led_cs_io_out
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PASEL1_L             020C 
           LOCAL                   ADDRESS         
           =====                   =======         
           PASEL1                  020C 
           PASEL1_H                020D 
           PASEL1_L                020C 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0222 - 0223 (0x2 bytes), align: 0
  Segment part 16. ROOT       Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PBOUT_L              0222 
           LOCAL                   ADDRESS         
           =====                   =======         
           PBOUT                   0222 
           PBOUT_H                 0223 
           PBOUT_L                 0222 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0224 - 0225 (0x2 bytes), align: 0
  Segment part 17. ROOT       Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PBDIR_L              0224 
           LOCAL                   ADDRESS         
           =====                   =======         
           PBDIR                   0224 
           PBDIR_H                 0225 
           PBDIR_L                 0224 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 022A - 022B (0x2 bytes), align: 0
  Segment part 18. ROOT       Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PBSEL0_L             022A 
           LOCAL                   ADDRESS         
           =====                   =======         
           PBSEL0                  022A 
           PBSEL0_H                022B 
           PBSEL0_L                022A 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 022C - 022D (0x2 bytes), align: 0
  Segment part 19. ROOT       Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_PBSEL1_L             022C 
           LOCAL                   ADDRESS         
           =====                   =======         
           PBSEL1                  022C 
           PBSEL1_H                022D 
           PBSEL1_L                022C 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0164 - 0165 (0x2 bytes), align: 0
  Segment part 20. ROOT       Intra module refs:   battery_AD_int
                                                   charge_AD_int
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_SYSCFG2_L            0164 
           LOCAL                   ADDRESS         
           =====                   =======         
           SYSCFG2                 0164 
           SYSCFG2_H               0165 
           SYSCFG2_L               0164 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0380 - 0381 (0x2 bytes), align: 0
  Segment part 21. ROOT       Intra module refs:   Timer_A_con_100us
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA0CTL                  0380 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0382 - 0383 (0x2 bytes), align: 0
  Segment part 22. ROOT       Intra module refs:   Timer_A_con_100us
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA0CCTL0                0382 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0384 - 0385 (0x2 bytes), align: 0
  Segment part 23. ROOT 
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA0CCTL1                0384 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0392 - 0393 (0x2 bytes), align: 0
  Segment part 24. ROOT       Intra module refs:   Timer_A_con_100us
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA0CCR0                 0392 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0394 - 0395 (0x2 bytes), align: 0
  Segment part 25. ROOT 
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA0CCR1                 0394 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 03C0 - 03C1 (0x2 bytes), align: 0
  Segment part 26. ROOT       Intra module refs:   Timer_B_con
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA1CTL                  03C0 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 03C2 - 03C3 (0x2 bytes), align: 0
  Segment part 27. ROOT       Intra module refs:   Timer_B_con
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA1CCTL0                03C2 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 03C6 - 03C7 (0x2 bytes), align: 0
  Segment part 28. ROOT       Intra module refs:   Timer_B_con
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA1CCTL2                03C6 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 03D2 - 03D3 (0x2 bytes), align: 0
  Segment part 29. ROOT       Intra module refs:   Timer_B_con
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA1CCR0                 03D2 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 03D6 - 03D7 (0x2 bytes), align: 0
  Segment part 30. ROOT       Intra module refs:   Timer_B_con
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA1CCR2                 03D6 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0400 - 0401 (0x2 bytes), align: 0
  Segment part 31. ROOT       Intra module refs:   Timer_C_con
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA2CTL                  0400 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0402 - 0403 (0x2 bytes), align: 0
  Segment part 32. ROOT       Intra module refs:   Timer_C_con
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA2CCTL0                0402 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0412 - 0413 (0x2 bytes), align: 0
  Segment part 33. ROOT       Intra module refs:   Timer_C_con
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA2CCR0                 0412 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0440 - 0441 (0x2 bytes), align: 0
  Segment part 34. ROOT       Intra module refs:   Port_1
                                                   Timer3_A0_ISR
                                                   Timer_D_con
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA3CTL                  0440 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0442 - 0443 (0x2 bytes), align: 0
  Segment part 35. ROOT       Intra module refs:   Timer_D_con
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA3CCTL0                0442 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 0452 - 0453 (0x2 bytes), align: 0
  Segment part 36. ROOT       Intra module refs:   Timer_D_con
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           TA3CCR0                 0452 
    -------------------------------------------------------------------------
DATA16_AN
  Relative segment, address: 01CC - 01CD (0x2 bytes), align: 0
  Segment part 37. ROOT       Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_WDTCTL_L             01CC 
           LOCAL                   ADDRESS         
           =====                   =======         
           WDTCTL                  01CC 
           WDTCTL_H                01CD 
           WDTCTL_L                01CC 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2062 - 2063 (0x2 bytes), align: 1
  Segment part 45.            Intra module refs:   Port_1
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           main_state              2062 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2064 - 2067 (0x4 bytes), align: 1
  Segment part 46.            Intra module refs:   Timer2_A0_ISR
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           LED_timer_cnt           2064 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2068 - 2069 (0x2 bytes), align: 1
  Segment part 47.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           state_2_led             2068 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 206A - 206B (0x2 bytes), align: 1
  Segment part 48.            Intra module refs:   Port_1
                                                   Timer2_A0_ISR
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_timer_count          206A 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 206C - 206D (0x2 bytes), align: 1
  Segment part 49.            Intra module refs:   Port_1
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_bit_state            206C 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 206E - 206F (0x2 bytes), align: 1
  Segment part 50.            Intra module refs:   Port_1
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_bit_reg              206E 
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 2000 - 2045 (0x46 bytes), align: 1
  Segment part 51.            Intra module refs:   Port_1
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_receive              2000            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 2046 - 2047 (0x2 bytes), align: 1
  Segment part 53.            Intra module refs:   Port_1
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_receive_cunt         2046 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2070 - 2071 (0x2 bytes), align: 1
  Segment part 55.            Intra module refs:   Port_1
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_bit_over             2070            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 2048 - 2049 (0x2 bytes), align: 1
  Segment part 56.            Intra module refs:   Port_1
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_bit_check            2048            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2072 - 2073 (0x2 bytes), align: 1
  Segment part 58.            Intra module refs:   Port_1
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_bit_check_1          2072 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2074 - 2075 (0x2 bytes), align: 1
  Segment part 60.            Intra module refs:   Port_1
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_start_stop           2074 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2076 - 2077 (0x2 bytes), align: 1
  Segment part 62.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_stimulate_add        2076            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2078 - 2079 (0x2 bytes), align: 1
  Segment part 63.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_command              2078            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 204A - 204D (0x4 bytes), align: 1
  Segment part 64.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_frequency            204A            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 204E - 204F (0x2 bytes), align: 1
  Segment part 66.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_Pulse_width          204E            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 2050 - 2051 (0x2 bytes), align: 1
  Segment part 68.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_pulse_number         2050            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 2052 - 2053 (0x2 bytes), align: 1
  Segment part 70.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_crruent_1            2052            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 2054 - 2055 (0x2 bytes), align: 1
  Segment part 72.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_crruent_2            2054            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 2056 - 2057 (0x2 bytes), align: 1
  Segment part 74.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_pulse_group_number   2056            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 2058 - 2059 (0x2 bytes), align: 1
  Segment part 76.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_pulse_group_interval_time
                                   2058            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 205A - 205B (0x2 bytes), align: 1
  Segment part 78.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_channel_selection    205A            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 205C - 205D (0x2 bytes), align: 1
  Segment part 80.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_delay_stimulate_time
                                   205C            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 207A - 207B (0x2 bytes), align: 1
  Segment part 82.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_off_on_stimulate     207A            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 207C - 207D (0x2 bytes), align: 1
  Segment part 83.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_pwm_en               207C            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 205E - 205F (0x2 bytes), align: 1
  Segment part 84.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IR_pwm_count            205E            IR_decode_reception (ir)
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 207E - 207F (0x2 bytes), align: 1
  Segment part 86.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           frequency_timer         207E 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2080 - 2083 (0x4 bytes), align: 1
  Segment part 87.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           pulse_group_interval_time
                                   2080 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2084 - 2087 (0x4 bytes), align: 1
  Segment part 88.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           pulse_group_number      2084 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2088 - 2089 (0x2 bytes), align: 1
  Segment part 89.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           pulse_number            2088 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 208A - 208B (0x2 bytes), align: 1
  Segment part 96.            Intra module refs:   Timer0_A0_ISR
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           timerA_cunt             208A 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 208C - 208D (0x2 bytes), align: 1
  Segment part 97.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           freq_state              208C 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 208E - 208F (0x2 bytes), align: 1
  Segment part 98.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           group_state             208E 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2090 - 2091 (0x2 bytes), align: 1
  Segment part 99.            Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           battery_AD_count        2090 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2092 - 2093 (0x2 bytes), align: 1
  Segment part 100.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           battery_AD_value        2092 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2094 - 2095 (0x2 bytes), align: 1
  Segment part 101.           Intra module refs:   battery_AD_int
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           battery_AD_value_sum    2094 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2096 - 2097 (0x2 bytes), align: 1
  Segment part 102.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           battery_AD_delayed      2096 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 2098 - 2099 (0x2 bytes), align: 1
  Segment part 103.           Intra module refs:   charge_AD_int
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           charge_AD_value_sum     2098 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 209A - 209B (0x2 bytes), align: 1
  Segment part 104.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           charge_AD_value         209A 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 209C - 209D (0x2 bytes), align: 1
  Segment part 105.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           us_to_ms                209C 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CEDA - CEDF (0x6 bytes), align: 1
  Segment part 111.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           LED_red_ON              CEDA 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CECA - CED1 (0x8 bytes), align: 1
  Segment part 112.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           LED_gree_ON             CECA 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CEE0 - CEE5 (0x6 bytes), align: 1
  Segment part 113.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           LED_red_OFF             CEE0 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CED2 - CED9 (0x8 bytes), align: 1
  Segment part 114.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           LED_gree_OFF            CED2 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CE1E - CE3D (0x20 bytes), align: 1
  Segment part 115.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           HallSensorSetup         CE1E 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CE6C - CE7F (0x14 bytes), align: 1
  Segment part 117.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer_A_con_100us       CE6C 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CDCE - CDFD (0x30 bytes), align: 1
  Segment part 118.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer_B_con             CDCE 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CE3E - CE57 (0x1a bytes), align: 1
  Segment part 121.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           config_led_cs_io_out    CE3E 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CE80 - CE93 (0x14 bytes), align: 1
  Segment part 122.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer_C_con             CE80 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CE94 - CEA7 (0x14 bytes), align: 1
  Segment part 123.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer_D_con             CE94 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CD98 - CDCD (0x36 bytes), align: 1
  Segment part 124.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           battery_AD_int          CD98 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CD60 - CD97 (0x38 bytes), align: 1
  Segment part 125.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           charge_AD_int           CD60 
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: C63A - CC2D (0x5f4 bytes), align: 1
  Segment part 126.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           main                    C63A            Segment part 27 (?cstart)
               calls direct
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 209E - 209F (0x2 bytes), align: 1
  Segment part 107.           Intra module refs:   Port_1
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           test                    209E 
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 2060 - 2061 (0x2 bytes), align: 1
  Segment part 108.           Intra module refs:   Port_1
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ir_state                2060 
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 20A0 - 20A1 (0x2 bytes), align: 1
  Segment part 110.           Intra module refs:   Port_1
                                                   Timer3_A0_ISR
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ir_stop_count           20A0 
    -------------------------------------------------------------------------
ISR_CODE
  Relative segment, address: C48A - C5E7 (0x15e bytes), align: 1
  Segment part 127.           Intra module refs:   Port_1::??INTVEC 84
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Port_1                  C48A 
               interrupt function
    -------------------------------------------------------------------------
ISR_CODE
  Relative segment, address: C5E8 - C5ED (0x6 bytes), align: 1
  Segment part 128.           Intra module refs:   Timer0_A0_ISR::??INTVEC 112
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer0_A0_ISR           C5E8 
               interrupt function
    -------------------------------------------------------------------------
ISR_CODE
  Relative segment, address: C5EE - C5EF (0x2 bytes), align: 1
  Segment part 129.           Intra module refs:   Timer1_A0_ISR::??INTVEC 108
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer1_A0_ISR           C5EE 
               interrupt function
    -------------------------------------------------------------------------
ISR_CODE
  Relative segment, address: C5F0 - C615 (0x26 bytes), align: 1
  Segment part 130.           Intra module refs:   Timer2_A0_ISR::??INTVEC 104
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer2_A0_ISR           C5F0 
               interrupt function
    -------------------------------------------------------------------------
ISR_CODE
  Relative segment, address: C616 - C62F (0x1a bytes), align: 1
  Segment part 131.           Intra module refs:   Timer3_A0_ISR::??INTVEC 100
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer3_A0_ISR           C616 
               interrupt function
    -------------------------------------------------------------------------
ISR_CODE
  Relative segment, address: C630 - C639 (0xa bytes), align: 1
  Segment part 132.           Intra module refs:   ADC_ISR::??INTVEC 86
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ADC_ISR                 C630 
               interrupt function
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: FF88 - FFDD (0x56 bytes), align: 1
  Segment part 38. ROOT 
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Port_1::??INTVEC 84     FFDC 
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: FF88 - FFDF (0x58 bytes), align: 1
  Segment part 39. ROOT 
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ADC_ISR::??INTVEC 86    FFDE 
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: FF88 - FFED (0x66 bytes), align: 1
  Segment part 40. ROOT 
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer3_A0_ISR::??INTVEC 100
                                   FFEC 
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: FF88 - FFF1 (0x6a bytes), align: 1
  Segment part 41. ROOT 
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer2_A0_ISR::??INTVEC 104
                                   FFF0 
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: FF88 - FFF5 (0x6e bytes), align: 1
  Segment part 42. ROOT 
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer1_A0_ISR::??INTVEC 108
                                   FFF4 
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: FF88 - FFF9 (0x72 bytes), align: 1
  Segment part 43. ROOT 
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           Timer0_A0_ISR::??INTVEC 112
                                   FFF8 
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C400 - C445 (0x46 bytes), align: 1
  Segment part 52.            Intra module refs:   IR_receive
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C446 - C447 (0x2 bytes), align: 1
  Segment part 54.            Intra module refs:   IR_receive_cunt
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C448 - C449 (0x2 bytes), align: 1
  Segment part 57.            Intra module refs:   IR_bit_check
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C44A - C44D (0x4 bytes), align: 1
  Segment part 65.            Intra module refs:   IR_frequency
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C44E - C44F (0x2 bytes), align: 1
  Segment part 67.            Intra module refs:   IR_Pulse_width
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C450 - C451 (0x2 bytes), align: 1
  Segment part 69.            Intra module refs:   IR_pulse_number
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C452 - C453 (0x2 bytes), align: 1
  Segment part 71.            Intra module refs:   IR_crruent_1
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C454 - C455 (0x2 bytes), align: 1
  Segment part 73.            Intra module refs:   IR_crruent_2
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C456 - C457 (0x2 bytes), align: 1
  Segment part 75.            Intra module refs:   IR_pulse_group_number
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C458 - C459 (0x2 bytes), align: 1
  Segment part 77.            Intra module refs:   IR_pulse_group_interval_time
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C45A - C45B (0x2 bytes), align: 1
  Segment part 79.            Intra module refs:   IR_channel_selection
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C45C - C45D (0x2 bytes), align: 1
  Segment part 81.            Intra module refs:   IR_delay_stimulate_time
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C45E - C45F (0x2 bytes), align: 1
  Segment part 85.            Intra module refs:   IR_pwm_count
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C460 - C461 (0x2 bytes), align: 1
  Segment part 109.           Intra module refs:   ir_state

    *************************************************************************

  FILE NAME : D:\Program Files (x86)\IAR Systems\Embedded Workbench 8.0\430\lib\dlib\dl430xlsfn.r43
  LIBRARY MODULE, NAME : ?__dbg_break

  SEGMENTS IN THE MODULE
  ======================
<CODE> 1 (was CODE)
  Relative segment, address: CEF2 - CEF3 (0x2 bytes), align: 1
  Segment part 2.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __DebugBreak            CEF2            __exit (?__exit)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?__exit

  SEGMENTS IN THE MODULE
  ======================
<CODE> 1 (was CODE)
  Relative segment, address: CE58 - CE6B (0x14 bytes), align: 1
  Segment part 2.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __exit                  CE58            Segment part 6 (?_exit)
                                                   _exit (?_exit)
               calls direct

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?_exit

  SEGMENTS IN THE MODULE
  ======================
<CODE> 1 (was CODE)
  Relative segment, address: CEEA, align: 1
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _exit                   CEEA            exit (?exit)
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CEEA - CEED (0x4 bytes), align: 1
  Segment part 6.             Intra module refs:   _exit

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?cstart

  SEGMENTS IN THE MODULE
  ======================
CSTACK
  Relative segment, address: 2F60, align: 1
  Segment part 1.
    -------------------------------------------------------------------------
DATA16_Z
  Relative segment, address: 20A2, align: 1
  Segment part 2.
    -------------------------------------------------------------------------
DATA16_I
  Relative segment, address: 2062, align: 1
  Segment part 3.
    -------------------------------------------------------------------------
DATA16_ID
  Relative segment, address: C462, align: 1
  Segment part 4.
    -------------------------------------------------------------------------
CODE_I
  Relative segment, address: 20A2, align: 1
  Segment part 5.
    -------------------------------------------------------------------------
CODE_ID
  Relative segment, address: C63A, align: 1
  Segment part 6.
    -------------------------------------------------------------------------
DATA20_Z
  Relative segment, address: 20A2, align: 1
  Segment part 7.
    -------------------------------------------------------------------------
DATA20_I
  Relative segment, address: 20A2, align: 1
  Segment part 8.
    -------------------------------------------------------------------------
DATA20_ID
  Relative segment, address: C462, align: 1
  Segment part 9.
    -------------------------------------------------------------------------
DATA20_C
  Relative segment, address: C462, align: 1
  Segment part 10.
    -------------------------------------------------------------------------
DATA20_N
  Relative segment, address: 20A2, align: 1
  Segment part 12.
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: C462 - C465 (0x4 bytes), align: 1
  Segment part 15.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __program_start         C462            ?reset_vector (?reset_vector)
                                                   Absolute parts (?ABS_ENTRY_MOD)
           ?cstart_begin           C462 
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: C466 - C471 (0xc bytes), align: 1
  Segment part 20.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?cstart_init_zero       C466            IR_bit_check_1 (main)
                                                   IR_bit_over (main)
                                                   IR_bit_reg (main)
                                                   IR_bit_state (main)
                                                   IR_command (main)
                                                   IR_off_on_stimulate (main)
                                                   IR_pwm_en (main)
                                                   IR_start_stop (main)
                                                   IR_stimulate_add (main)
                                                   IR_timer_count (main)
                                                   LED_timer_cnt (main)
                                                   battery_AD_count (main)
                                                   battery_AD_delayed (main)
                                                   battery_AD_value (main)
                                                   battery_AD_value_sum (main)
                                                   charge_AD_value (main)
                                                   charge_AD_value_sum (main)
                                                   freq_state (main)
                                                   frequency_timer (main)
                                                   group_state (main)
                                                   ir_stop_count (main)
                                                   main_state (main)
                                                   pulse_group_interval_time (main)
                                                   pulse_group_number (main)
                                                   pulse_number (main)
                                                   state_2_led (main)
                                                   test (main)
                                                   timerA_cunt (main)
                                                   us_to_ms (main)
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: C472 - C481 (0x10 bytes), align: 1
  Segment part 21.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?cstart_init_copy       C472            IR_Pulse_width (main)
                                                   IR_bit_check (main)
                                                   IR_channel_selection (main)
                                                   IR_crruent_1 (main)
                                                   IR_crruent_2 (main)
                                                   IR_delay_stimulate_time (main)
                                                   IR_frequency (main)
                                                   IR_pulse_group_interval_time (main)
                                                   IR_pulse_group_number (main)
                                                   IR_pulse_number (main)
                                                   IR_pwm_count (main)
                                                   IR_receive (main)
                                                   IR_receive_cunt (main)
                                                   ir_state (main)
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: C482 - C489 (0x8 bytes), align: 1
  Segment part 27.            Intra module refs:   Segment part 15
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?cstart_call_main       C482 
           ?cstart_end             C48A 
    -------------------------------------------------------------------------
CODE_ID
  Relative segment, address: C63A, align: 1
  Segment part 28.            Intra module refs:   Segment part 5

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?reset_vector

  SEGMENTS IN THE MODULE
  ======================
RESET
  Relative segment, address: FFFE - FFFF (0x2 bytes), align: 1
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?reset_vector           FFFE            Segment part 15 (?cstart)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?exit

  SEGMENTS IN THE MODULE
  ======================
<CODE> 1 (was CODE)
  Relative segment, address: CEE6 - CEE9 (0x4 bytes), align: 1
  Segment part 2.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           exit                    CEE6            Segment part 27 (?cstart)
               calls direct

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?memcpy

  SEGMENTS IN THE MODULE
  ======================
<CODE> 1 (was CODE)
  Relative segment, address: CEA8 - CEB9 (0x12 bytes), align: 1
  Segment part 2.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           memcpy                  CEA8            __data16_memcpy (?memzero)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?memzero

  SEGMENTS IN THE MODULE
  ======================
<CODE> 1 (was CODE)
  Relative segment, address: CEBA - CEC9 (0x10 bytes), align: 1
  Segment part 2.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __data16_memzero        CEBA            ?cstart_init_zero (?cstart)
    -------------------------------------------------------------------------
<CODE> 1 (was CODE)
  Relative segment, address: CEEE - CEF1 (0x4 bytes), align: 1
  Segment part 3.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __data16_memcpy         CEEE            ?cstart_init_copy (?cstart)
               calls direct




                ****************************************
                *                                      *
                *      SEGMENTS IN ADDRESS ORDER       *
                *                                      *
                ****************************************


SEGMENT              SPACE    START ADDRESS   END ADDRESS     SIZE  TYPE  ALIGN
=======              =====    =============   ===========     ====  ====  =====
DATA16_AN                              0130 - 0131               2   rel    0
                                       0164 - 0165               2 
                                       01CC - 01CD               2 
                                       0202 - 0207               6 
                                       020A - 020D               4 
                                       0218 - 021D               6 
                                       0222 - 0225               4 
                                       022A - 022D               4 
                                       0380 - 0385               6 
                                       0392 - 0395               4 
                                       03C0 - 03C3               4 
                                       03C6 - 03C7               2 
                                       03D2 - 03D3               2 
                                       03D6 - 03D7               2 
                                       0400 - 0403               4 
                                       0412 - 0413               2 
                                       0440 - 0443               4 
                                       0452 - 0453               2 
                                       0700 - 0703               4 
                                       070A - 070B               2 
                                       0712 - 0713               2 
                                       071A - 071D               4 
DATA16_I                               2000 - 2061              62   rel    1
DATA16_Z                               2062 - 20A1              40   rel    1
CODE_I                                    20A2                       rel    1
DATA20_I                                  20A2                       rel    1
DATA20_Z                                  20A2                       rel    1
DATA20_N                                  20A2                       rel    1
CSTACK                                 2F60 - 2FFF              A0   rel    1
DATA16_C                                  C400                       dse    0
DATA16_ID                              C400 - C461              62   rel    1
DATA20_C                                  C462                       rel    1
DATA20_ID                                 C462                       rel    1
CSTART                                 C462 - C489              28   rel    1
ISR_CODE                               C48A - C639             1B0   rel    1
CODE_ID                                   C63A                       rel    1
<CODE> 1                               C63A - CEF3             8BA   rel    1
INTVEC                                 FF88 - FFF9              72   com    1
RESET                                  FFFE - FFFF               2   rel    1

                ****************************************
                *                                      *
                *        END OF CROSS REFERENCE        *
                *                                      *
                ****************************************

 2 822 bytes of CODE  memory
   322 bytes of DATA  memory (+ 74 absolute )
    98 bytes of CONST memory

Errors: none
Warnings: none

