<?xml version="1.0"?>
<Workspace>
    <ConfigDictionary>
        <CurrentConfigs>
            <Project>light_stim_v1.0/Debug</Project>
        </CurrentConfigs>
    </ConfigDictionary>
    <WindowStorage>
        <ChildIdMap>
            <TB_MAIN>34048</TB_MAIN>
            <WIN_BUILD>34049</WIN_BUILD>
            <WIN_CALL_GRAPH>34050</WIN_CALL_GRAPH>
            <WIN_C_STAT>34051</WIN_C_STAT>
            <WIN_FIND_ALL_DECLARATIONS>34052</WIN_FIND_ALL_DECLARATIONS>
            <WIN_FIND_ALL_REFERENCES>34053</WIN_FIND_ALL_REFERENCES>
            <WIN_FIND_IN_FILES>34054</WIN_FIND_IN_FILES>
            <WIN_SELECT_AMBIGUOUS_DEFINITIONS>34055</WIN_SELECT_AMBIGUOUS_DEFINITIONS>
            <WIN_SOURCE_BROWSER>34056</WIN_SOURCE_BROWSER>
            <WIN_TOOL_OUTPUT>34057</WIN_TOOL_OUTPUT>
            <WIN_WORKSPACE>34058</WIN_WORKSPACE>
            <WIN_BREAKPOINTS>34059</WIN_BREAKPOINTS>
            <WIN_CUSTOM_SFR>34060</WIN_CUSTOM_SFR>
            <WIN_CYCLECOUNTER5XX>34061</WIN_CYCLECOUNTER5XX>
            <WIN_DEBUG_LOG>34062</WIN_DEBUG_LOG>
            <WIN_DEVICEINFORMATIONWINDOW>34063</WIN_DEVICEINFORMATIONWINDOW>
            <WIN_SEQUENCERDIALOG>34064</WIN_SEQUENCERDIALOG>
            <WIN_STATESTORAGEDIALOG>34065</WIN_STATESTORAGEDIALOG>
        </ChildIdMap>
        <Desktop>
            <IarPane-34048>
                <ToolBarCmdIds>
                    <item>57600</item>
                    <item>57601</item>
                    <item>57603</item>
                    <item>33024</item>
                    <item>0</item>
                    <item>57607</item>
                    <item>0</item>
                    <item>57635</item>
                    <item>57634</item>
                    <item>57637</item>
                    <item>0</item>
                    <item>57643</item>
                    <item>57644</item>
                    <item>0</item>
                    <item>33090</item>
                    <item>33057</item>
                    <item>57636</item>
                    <item>57640</item>
                    <item>57641</item>
                    <item>33026</item>
                    <item>33065</item>
                    <item>33063</item>
                    <item>33064</item>
                    <item>33053</item>
                    <item>33054</item>
                    <item>0</item>
                    <item>33035</item>
                    <item>33036</item>
                    <item>34399</item>
                    <item>0</item>
                    <item>33038</item>
                    <item>33039</item>
                    <item>0</item>
                </ToolBarCmdIds>
            </IarPane-34048>
            <IarPane-34058>
                <ColumnWidths>
                    <Column0>190</Column0>
                    <Column1>30</Column1>
                    <Column2>30</Column2>
                    <Column3>30</Column3>
                </ColumnWidths>
                <NodeDict>
                    <ExpandedNode>light_stim_v1.0</ExpandedNode>
                </NodeDict>
            </IarPane-34058>
            <IarPane-34062>
                <ColumnWidth0>29</ColumnWidth0>
                <ColumnWidth1>1208</ColumnWidth1>
                <FilterLevel>2</FilterLevel>
                <LiveFile></LiveFile>
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34062>
            <ControlBarVersion>
                <Major>14</Major>
                <Minor>16</Minor>
            </ControlBarVersion>
            <MFCToolBarParameters>
                <Tooltips>1</Tooltips>
                <ShortcutKeys>1</ShortcutKeys>
                <LargeIcons>0</LargeIcons>
                <MenuAnimation>0</MenuAnimation>
                <RecentlyUsedMenus>1</RecentlyUsedMenus>
                <MenuShadows>1</MenuShadows>
                <ShowAllMenusAfterDelay>1</ShowAllMenusAfterDelay>
                <CommandsUsage>060B000029005984000001000000138600000200000040E1000001000000298100000100000010860000010000002CE1000002000000568400000A00000029E10000040000005F8600000500000020810000010000000C810000550500001D810000550000000D80000006000000048600000100000019820000190000004A81000001000000288100000100000017810000020000000384000003000000568600001100000016820000230000001481000002000000558400008C0200002BE1000045000000008100001F0000000E810000B301000003E10000210000005E860000010000001F810000010000000B8100000600000000E100000800000018820000230000001486000001000000028400000400000011860000030000004681000001000000608600001700000024E10000020000001E8100000200000002E10000030000005D86000001000000</CommandsUsage>
            </MFCToolBarParameters>
            <CommandManager>
                <CommandsWithoutImages>55007784000007840000FFFFFFFF808C00000D8400000F8400000884000054840000328100001C8100000984000044D5000055840000568400005984000034920000339200002F92000030920000259200002B9200002A920000269200001E920000459C0000029E0000129E00000F9E0000259E0000429C0000439C00004D9C0000089E0000509C0000359E0000549C00001E9E0000489C0000209E0000449C00003C8400003D840000408400004C8400003E8400004B8400004D8400003F8400003A8400003B8400005A8400005B8400002AE10000008200001C82000001820000678600005384000014820000158200001A8200001B82000039810000178200002F84000022810000238100000C84000033840000788400001184000008800000098000000A8000000B8000000C800000158000000A81000001E80000498100004A8100002F820000168200001882000019820000</CommandsWithoutImages>
                <MenuUserImages>3A00298100002900000004840000450000000481000015000000158100001E0000002CE1000038000000018100000F00000029E100003600000007E10000340000002392000000000000318400004C0000000F8100001C000000208100002400000004E10000320000005F860000290000003F810000260000001D810000210000000C810000190000000D8000001000000001E100002F00000023E100003200000009810000170000000684000047000000288100002800000017810000200000000384000044000000148100001D0000002BE1000037000000008400004100000028E10000350000000081000012000000308400004B0000000E840000490000000E8100001B0000001F8100002300000003E100003100000025E10000340000000B8100001800000000E100002E00000022E10000310000002B8000000D00000041E100003E000000058400004600000005810000160000002781000027000000168100001F00000002840000430000000281000014000000328400004D000000108400004A0000002181000021000000518400004F00000005E100003300000035E100003D00000024E10000330000001E810000220000000D8100001A00000002E10000300000000A84000048000000</MenuUserImages>
            </CommandManager>
            <Pane-59393>
                <ID>0</ID>
                <RectRecentFloat>0A0000000A0000006E0000006E000000</RectRecentFloat>
                <RectRecentDocked>000000000E0300000006000021030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-59393>
            <BasePane-59393>
                <IsVisible>1</IsVisible>
            </BasePane-59393>
            <Pane--1>
                <ID>4294967295</ID>
                <RectRecentFloat>0A010000E90100000006000078020000</RectRecentFloat>
                <RectRecentDocked>0A010000D20100000006000061020000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane--1>
            <BasePane--1>
                <IsVisible>0</IsVisible>
            </BasePane--1>
            <Pane-34049>
                <ID>34049</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>0000000079020000000600000E030000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34049>
            <BasePane-34049>
                <IsVisible>1</IsVisible>
            </BasePane-34049>
            <IarPane-34049>
                <ColumnWidth0>21</ColumnWidth0>
                <ColumnWidth1>1120</ColumnWidth1>
                <ColumnWidth2>298</ColumnWidth2>
                <ColumnWidth3>74</ColumnWidth3>
                <FilterLevel>2</FilterLevel>
                <LiveFile>H:\cheng_xu\msp430\MSP430FR2433\light_stim\light_stim_V1.0 - 20220926\BuildLog.log</LiveFile>
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34049>
            <Pane-34052>
                <ID>34052</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>040000007D020000FC050000F4020000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34052>
            <BasePane-34052>
                <IsVisible>0</IsVisible>
            </BasePane-34052>
            <IarPane-34052>
                <ColumnWidth0>532</ColumnWidth0>
                <ColumnWidth1>76</ColumnWidth1>
                <ColumnWidth2>912</ColumnWidth2>
                <FilterLevel>2</FilterLevel>
                <LiveFile />
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34052>
            <Pane-34053>
                <ID>34053</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>040000007D020000FC050000F4020000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34053>
            <BasePane-34053>
                <IsVisible>0</IsVisible>
            </BasePane-34053>
            <IarPane-34053 />
            <Pane-34054>
                <ID>34054</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>040000007D020000FC050000F4020000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34054>
            <BasePane-34054>
                <IsVisible>0</IsVisible>
            </BasePane-34054>
            <IarPane-34054 />
            <Pane-34055>
                <ID>34055</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>040000007D020000FC050000F4020000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34055>
            <BasePane-34055>
                <IsVisible>0</IsVisible>
            </BasePane-34055>
            <IarPane-34055 />
            <Pane-34057>
                <ID>34057</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>040000007D020000FC050000F4020000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34057>
            <BasePane-34057>
                <IsVisible>0</IsVisible>
            </BasePane-34057>
            <IarPane-34057 />
            <Pane-34050>
                <ID>34050</ID>
                <RectRecentFloat>000000001700000080020000A8000000</RectRecentFloat>
                <RectRecentDocked>00000000000000008002000091000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34050>
            <BasePane-34050>
                <IsVisible>0</IsVisible>
            </BasePane-34050>
            <IarPane-34050 />
            <Pane-34051>
                <ID>34051</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000022010000B1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34051>
            <BasePane-34051>
                <IsVisible>0</IsVisible>
            </BasePane-34051>
            <IarPane-34051 />
            <Pane-34056>
                <ID>34056</ID>
                <RectRecentFloat>00000000170000000601000078010000</RectRecentFloat>
                <RectRecentDocked>040000004A0000000201000047020000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34056>
            <BasePane-34056>
                <IsVisible>0</IsVisible>
            </BasePane-34056>
            <IarPane-34056>
                <TypeFilter>2147483647</TypeFilter>
                <FileFilter>1</FileFilter>
            </IarPane-34056>
            <Pane-34058>
                <ID>34058</ID>
                <RectRecentFloat>00000000170000000601000078010000</RectRecentFloat>
                <RectRecentDocked>00000000460000000601000061020000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34058>
            <BasePane-34058>
                <IsVisible>1</IsVisible>
            </BasePane-34058>
            <Pane-34059>
                <ID>34059</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>0E010000EA010000FC05000047020000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34059>
            <BasePane-34059>
                <IsVisible>0</IsVisible>
            </BasePane-34059>
            <IarPane-34059 />
            <Pane-34062>
                <ID>34062</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>0E010000EA010000FC05000047020000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34062>
            <BasePane-34062>
                <IsVisible>0</IsVisible>
            </BasePane-34062>
            <Pane-34060>
                <ID>34060</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000022010000B1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34060>
            <BasePane-34060>
                <IsVisible>0</IsVisible>
            </BasePane-34060>
            <IarPane-34060 />
            <Pane-34061>
                <ID>34061</ID>
                <RectRecentFloat>000000001700000050000000F8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000050000000E1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34061>
            <BasePane-34061>
                <IsVisible>0</IsVisible>
            </BasePane-34061>
            <IarPane-34061 />
            <Pane-34063>
                <ID>34063</ID>
                <RectRecentFloat>00000000170000005000000068000000</RectRecentFloat>
                <RectRecentDocked>00000000000000005000000051000000</RectRecentDocked>
                <RecentFrameAlignment>16384</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34063>
            <BasePane-34063>
                <IsVisible>0</IsVisible>
            </BasePane-34063>
            <IarPane-34063 />
            <Pane-34064>
                <ID>34064</ID>
                <RectRecentFloat>000000001700000050000000D8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000050000000C1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34064>
            <BasePane-34064>
                <IsVisible>0</IsVisible>
            </BasePane-34064>
            <IarPane-34064 />
            <Pane-34065>
                <ID>34065</ID>
                <RectRecentFloat>00000000170000002201000068000000</RectRecentFloat>
                <RectRecentDocked>00000000000000002201000051000000</RectRecentDocked>
                <RecentFrameAlignment>16384</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34065>
            <BasePane-34065>
                <IsVisible>0</IsVisible>
            </BasePane-34065>
            <IarPane-34065 />
            <DockingManager-256>
                <DockingPaneAndPaneDividers>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</DockingPaneAndPaneDividers>
            </DockingManager-256>
            <MFCToolBar-34048>
                <Name>Main</Name>
                <Buttons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uttons>
            </MFCToolBar-34048>
            <Pane-34048>
                <ID>34048</ID>
                <RectRecentFloat>0A0000000A0000006E0000006E000000</RectRecentFloat>
                <RectRecentDocked>0000000000000000FE0200001A000000</RectRecentDocked>
                <RecentFrameAlignment>8192</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>744</MRUWidth>
                <PinState>0</PinState>
            </Pane-34048>
            <BasePane-34048>
                <IsVisible>1</IsVisible>
            </BasePane-34048>
        </Desktop>
        <MDIWindows>
            <MDIClientArea-0>
                <MDITabsState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absState>
            </MDIClientArea-0>
        </MDIWindows>
    </WindowStorage>
</Workspace>
