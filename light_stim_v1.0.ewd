<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>MSP430</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>C-SPY</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>27</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GoToEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GoToName</name>
                    <state>main</state>
                </option>
                <option>
                    <name>DynDriver</name>
                    <state>430FET</state>
                </option>
                <option>
                    <name>dDllSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>DdfFileSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>DdfOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>DdfFileName</name>
                    <state>$TOOLKIT_DIR$\config\debugger\msp430fr2433.ddf</state>
                </option>
                <option>
                    <name>ProcTMS</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>ProcMSP430X</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CompilerDataModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IVBASE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCImagesSuppressCheck1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCImagesPath1</name>
                    <state></state>
                </option>
                <option>
                    <name>OCImagesSuppressCheck2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCImagesPath2</name>
                    <state></state>
                </option>
                <option>
                    <name>OCImagesSuppressCheck3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCImagesPath3</name>
                    <state></state>
                </option>
                <option>
                    <name>CPUTAG</name>
                    <state>1</state>
                </option>
                <option>
                    <name>L092Mode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCImagesOffset1</name>
                    <state></state>
                </option>
                <option>
                    <name>OCImagesOffset2</name>
                    <state></state>
                </option>
                <option>
                    <name>OCImagesOffset3</name>
                    <state></state>
                </option>
                <option>
                    <name>OCImagesUse1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCImagesUse2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCImagesUse3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ENERGYTRACE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>FETIPE</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>430FET</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>32</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CFetMandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Erase</name>
                    <state>0</state>
                </option>
                <option>
                    <name>EMUVerifyDownloadP7</name>
                    <state>0</state>
                </option>
                <option>
                    <name>EraseOptionSlaveP7</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExitBreakpointP7</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PutcharBreakpointP7</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GetcharBreakpointP7</name>
                    <state>1</state>
                </option>
                <option>
                    <name>derivativeP7</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ParallelPortP7</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>TargetVoltage</name>
                    <state>3.3</state>
                </option>
                <option>
                    <name>AllowLockedFlashAccessP7</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CRadioProtocolType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCRadioModuleTypeSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>EEMLevel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>DiasbleMemoryCache</name>
                    <state>0</state>
                </option>
                <option>
                    <name>NeedLockedFlashAccess</name>
                    <state>1</state>
                </option>
                <option>
                    <name>UsbComPort</name>
                    <state>Automatic</state>
                </option>
                <option>
                    <name>FetConnection</name>
                    <version>5</version>
                    <state>0</state>
                </option>
                <option>
                    <name>SoftwareBreakpointEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>RadioSoftwareBreakpointType</name>
                    <state>1</state>
                </option>
                <option>
                    <name>TargetSettlingtime</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AllowAccessToBSL</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OTargetVccTypeDefault</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCBetaDll</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GPassword</name>
                    <state></state>
                </option>
                <option>
                    <name>DebugLPM5</name>
                    <state>0</state>
                </option>
                <option>
                    <name>LPM5Slave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CRadioAutoManualType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExternalCodeDownload</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCVCCDefault</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Retain</name>
                    <state>0</state>
                </option>
                <option>
                    <name>jstatebit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioJtagSpeedType</name>
                    <state>1</state>
                </option>
                <option>
                    <name>memoryTypeSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>fuseBlowDisabledSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>eraseTypeSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>DataSampleBpReservation</name>
                    <state>0</state>
                </option>
                <option>
                    <name>cycleCounterLevel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>variablewatch</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCLockDevice</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>SIM430</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>5</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>SimOddAddressCheckP7</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSimMandatory</name>
                    <state>1</state>
                </option>
                <option>
                    <name>derivativeSim</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <debuggerPlugins>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxPlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxTinyPlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\TI-RTOS\tirtosplugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-286-KA-CSpy.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-KA-CSpy.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-III\uCOS-III-KA-CSpy.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\CodeCoverage\CodeCoverage.ENU.ewplugin</file>
                <loadFlag>1</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
        </debuggerPlugins>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>MSP430</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>C-SPY</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>27</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GoToEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GoToName</name>
                    <state>main</state>
                </option>
                <option>
                    <name>DynDriver</name>
                    <state>SIM430</state>
                </option>
                <option>
                    <name>dDllSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>DdfFileSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>DdfOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>DdfFileName</name>
                    <state></state>
                </option>
                <option>
                    <name>ProcTMS</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>ProcMSP430X</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CompilerDataModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IVBASE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCImagesSuppressCheck1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCImagesPath1</name>
                    <state></state>
                </option>
                <option>
                    <name>OCImagesSuppressCheck2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCImagesPath2</name>
                    <state></state>
                </option>
                <option>
                    <name>OCImagesSuppressCheck3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCImagesPath3</name>
                    <state></state>
                </option>
                <option>
                    <name>CPUTAG</name>
                    <state>1</state>
                </option>
                <option>
                    <name>L092Mode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCImagesOffset1</name>
                    <state></state>
                </option>
                <option>
                    <name>OCImagesOffset2</name>
                    <state></state>
                </option>
                <option>
                    <name>OCImagesOffset3</name>
                    <state></state>
                </option>
                <option>
                    <name>OCImagesUse1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCImagesUse2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCImagesUse3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ENERGYTRACE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>FETIPE</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>430FET</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>32</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CFetMandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Erase</name>
                    <state>0</state>
                </option>
                <option>
                    <name>EMUVerifyDownloadP7</name>
                    <state>0</state>
                </option>
                <option>
                    <name>EraseOptionSlaveP7</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExitBreakpointP7</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PutcharBreakpointP7</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GetcharBreakpointP7</name>
                    <state>1</state>
                </option>
                <option>
                    <name>derivativeP7</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ParallelPortP7</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>TargetVoltage</name>
                    <state>###Uninitialized###</state>
                </option>
                <option>
                    <name>AllowLockedFlashAccessP7</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CRadioProtocolType</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRadioModuleTypeSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>EEMLevel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>DiasbleMemoryCache</name>
                    <state>0</state>
                </option>
                <option>
                    <name>NeedLockedFlashAccess</name>
                    <state>1</state>
                </option>
                <option>
                    <name>UsbComPort</name>
                    <state>Automatic</state>
                </option>
                <option>
                    <name>FetConnection</name>
                    <version>5</version>
                    <state>0</state>
                </option>
                <option>
                    <name>SoftwareBreakpointEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioSoftwareBreakpointType</name>
                    <state>1</state>
                </option>
                <option>
                    <name>TargetSettlingtime</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AllowAccessToBSL</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OTargetVccTypeDefault</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCBetaDll</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GPassword</name>
                    <state>###Uninitialized###</state>
                </option>
                <option>
                    <name>DebugLPM5</name>
                    <state>0</state>
                </option>
                <option>
                    <name>LPM5Slave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CRadioAutoManualType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExternalCodeDownload</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCVCCDefault</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Retain</name>
                    <state>0</state>
                </option>
                <option>
                    <name>jstatebit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioJtagSpeedType</name>
                    <state>1</state>
                </option>
                <option>
                    <name>memoryTypeSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>fuseBlowDisabledSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>eraseTypeSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>DataSampleBpReservation</name>
                    <state>0</state>
                </option>
                <option>
                    <name>cycleCounterLevel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>variablewatch</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCLockDevice</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>SIM430</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>5</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>SimOddAddressCheckP7</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSimMandatory</name>
                    <state>1</state>
                </option>
                <option>
                    <name>derivativeSim</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <debuggerPlugins>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxPlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxTinyPlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\TI-RTOS\tirtosplugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-286-KA-CSpy.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-KA-CSpy.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-III\uCOS-III-KA-CSpy.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\CodeCoverage\CodeCoverage.ENU.ewplugin</file>
                <loadFlag>1</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
        </debuggerPlugins>
    </configuration>
</project>
