"H:\cheng_xu\msp430\MSP430FR2433\light_stim\light_stim_V1.0 - 20220926_123456\ir.c"
-std=c99
-ferror-limit=0
-fbracket-depth=512
-I
D:\Program Files (x86)\IAR Systems\Embedded Workbench 8.0\430\inc\dlib
-I
D:\Program Files (x86)\IAR Systems\Embedded Workbench 8.0\430\inc
-I
D:\Program Files (x86)\IAR Systems\Embedded Workbench 8.0\430\inc\dlib\c
-D__CHAR_BITS__=8
-D__CHAR_MAX__=0xff
-D__CHAR_MIN__=0
-D__CHAR_SIZE__=1
-D__UNSIGNED_CHAR_MAX__=0xff
-D__SIGNED_CHAR_MAX__=127
-D__SIGNED_CHAR_MIN__=(-__SIGNED_CHAR_MAX__-1)
-D__CHAR_ALIGN__=1
-D__INT_SIZE__=2
-D__UNSIGNED_INT_MAX__=0xffffU
-D__SIGNED_INT_MAX__=32767
-D__SIGNED_INT_MIN__=(-__SIGNED_INT_MAX__-1)
-D__INT_ALIGN__=2
-D__SHORT_SIZE__=2
-D__UNSIGNED_SHORT_MAX__=0xffffU
-D__SIGNED_SHORT_MAX__=32767
-D__SIGNED_SHORT_MIN__=(-__SIGNED_SHORT_MAX__-1)
-D__SHORT_ALIGN__=2
-D__LONG_SIZE__=4
-D__UNSIGNED_LONG_MAX__=0xffffffffUL
-D__SIGNED_LONG_MAX__=2147483647L
-D__SIGNED_LONG_MIN__=(-__SIGNED_LONG_MAX__-1)
-D__LONG_ALIGN__=2
-D__LONG_LONG_SIZE__=8
-D__UNSIGNED_LONG_LONG_MAX__=0xffffffffffffffffULL
-D__SIGNED_LONG_LONG_MAX__=9223372036854775807LL
-D__SIGNED_LONG_LONG_MIN__=(-__SIGNED_LONG_LONG_MAX__-1)
-D__LONG_LONG_ALIGN__=2
-D__INT8_T_TYPE__=signed char
-D__INT8_T_MAX__=127
-D__INT8_T_MIN__=(-__INT8_T_MAX__-1)
-D__UINT8_T_TYPE__=unsigned char
-D__UINT8_T_MAX__=0xff
-D__INT8_SIZE_PREFIX__="hh"
-D__INT16_T_TYPE__=signed int
-D__INT16_T_MAX__=32767
-D__INT16_T_MIN__=(-__INT16_T_MAX__-1)
-D__UINT16_T_TYPE__=unsigned int
-D__UINT16_T_MAX__=0xffffU
-D__INT16_SIZE_PREFIX__=""
-D__INT32_T_TYPE__=signed long int
-D__INT32_T_MAX__=2147483647L
-D__INT32_T_MIN__=(-__INT32_T_MAX__-1)
-D__UINT32_T_TYPE__=unsigned long int
-D__UINT32_T_MAX__=0xffffffffUL
-D__INT32_SIZE_PREFIX__="l"
-D__INT64_T_TYPE__=signed long long int
-D__INT64_T_MAX__=9223372036854775807LL
-D__INT64_T_MIN__=(-__INT64_T_MAX__-1)
-D__UINT64_T_TYPE__=unsigned long long int
-D__UINT64_T_MAX__=0xffffffffffffffffULL
-D__INT64_SIZE_PREFIX__="ll"
-D__INT_LEAST8_T_TYPE__=signed char
-D__INT_LEAST8_T_MAX__=127
-D__INT_LEAST8_T_MIN__=(-__INT_LEAST8_T_MAX__-1)
-D__UINT_LEAST8_T_TYPE__=unsigned char
-D__UINT_LEAST8_T_MAX__=0xff
-D__INT8_C_SUFFIX__=
-D__UINT8_C_SUFFIX__=
-D__INT_LEAST8_SIZE_PREFIX__="hh"
-D__INT_LEAST16_T_TYPE__=signed int
-D__INT_LEAST16_T_MAX__=32767
-D__INT_LEAST16_T_MIN__=(-__INT_LEAST16_T_MAX__-1)
-D__UINT_LEAST16_T_TYPE__=unsigned int
-D__UINT_LEAST16_T_MAX__=0xffffU
-D__INT16_C_SUFFIX__=
-D__UINT16_C_SUFFIX__=U
-D__INT_LEAST16_SIZE_PREFIX__=""
-D__INT_LEAST32_T_TYPE__=signed long int
-D__INT_LEAST32_T_MAX__=2147483647L
-D__INT_LEAST32_T_MIN__=(-__INT_LEAST32_T_MAX__-1)
-D__UINT_LEAST32_T_TYPE__=unsigned long int
-D__UINT_LEAST32_T_MAX__=0xffffffffUL
-D__INT32_C_SUFFIX__=L
-D__UINT32_C_SUFFIX__=UL
-D__INT_LEAST32_SIZE_PREFIX__="l"
-D__INT_LEAST64_T_TYPE__=signed long long int
-D__INT_LEAST64_T_MAX__=9223372036854775807LL
-D__INT_LEAST64_T_MIN__=(-__INT_LEAST64_T_MAX__-1)
-D__UINT_LEAST64_T_TYPE__=unsigned long long int
-D__UINT_LEAST64_T_MAX__=0xffffffffffffffffULL
-D__INT64_C_SUFFIX__=LL
-D__UINT64_C_SUFFIX__=ULL
-D__INT_LEAST64_SIZE_PREFIX__="ll"
-D__INT_FAST8_T_TYPE__=signed char
-D__INT_FAST8_T_MAX__=127
-D__INT_FAST8_T_MIN__=(-__INT_FAST8_T_MAX__-1)
-D__UINT_FAST8_T_TYPE__=unsigned char
-D__UINT_FAST8_T_MAX__=0xff
-D__INT_FAST8_SIZE_PREFIX__="hh"
-D__INT_FAST16_T_TYPE__=signed int
-D__INT_FAST16_T_MAX__=32767
-D__INT_FAST16_T_MIN__=(-__INT_FAST16_T_MAX__-1)
-D__UINT_FAST16_T_TYPE__=unsigned int
-D__UINT_FAST16_T_MAX__=0xffffU
-D__INT_FAST16_SIZE_PREFIX__=""
-D__INT_FAST32_T_TYPE__=signed long int
-D__INT_FAST32_T_MAX__=2147483647L
-D__INT_FAST32_T_MIN__=(-__INT_FAST32_T_MAX__-1)
-D__UINT_FAST32_T_TYPE__=unsigned long int
-D__UINT_FAST32_T_MAX__=0xffffffffUL
-D__INT_FAST32_SIZE_PREFIX__="l"
-D__INT_FAST64_T_TYPE__=signed long long int
-D__INT_FAST64_T_MAX__=9223372036854775807LL
-D__INT_FAST64_T_MIN__=(-__INT_FAST64_T_MAX__-1)
-D__UINT_FAST64_T_TYPE__=unsigned long long int
-D__UINT_FAST64_T_MAX__=0xffffffffffffffffULL
-D__INT_FAST64_SIZE_PREFIX__="ll"
-D__INTMAX_T_TYPE__=signed long long int
-D__INTMAX_T_MAX__=9223372036854775807LL
-D__INTMAX_T_MIN__=(-__INTMAX_T_MAX__-1)
-D__UINTMAX_T_TYPE__=unsigned long long int
-D__UINTMAX_T_MAX__=0xffffffffffffffffULL
-D__INTMAX_C_SUFFIX__=LL
-D__UINTMAX_C_SUFFIX__=ULL
-D__INTMAX_SIZE_PREFIX__="ll"
-D__FLOAT_SIZE__=4
-D__FLOAT_ALIGN__=2
-D__DOUBLE_SIZE__=4
-D__DOUBLE_ALIGN__=2
-D__LONG_DOUBLE_SIZE__=4
-D__LONG_DOUBLE_ALIGN__=2
-D__NAN_HAS_HIGH_MANTISSA_BIT_SET__=1
-D__SUBNORMAL_FLOATING_POINTS__=0
-D__SIZE_T_TYPE__=unsigned int
-D__SIZE_T_MAX__=0xffffU
-D__PTRDIFF_T_TYPE__=signed int
-D__PTRDIFF_T_MAX__=32767
-D__PTRDIFF_T_MIN__=(-__PTRDIFF_T_MAX__-1)
-D__INTPTR_T_TYPE__=signed int
-D__INTPTR_T_MAX__=32767
-D__INTPTR_T_MIN__=(-__INTPTR_T_MAX__-1)
-D__UINTPTR_T_TYPE__=unsigned int
-D__UINTPTR_T_MAX__=0xffffU
-D__INTPTR_SIZE_PREFIX__=""
-D__JMP_BUF_ELEMENT_TYPE__=unsigned short int
-D__JMP_BUF_NUM_ELEMENTS__=20
-D__TID__=0xab10
-D__VER__=712
-D__SUBVERSION__=1
-D__BUILD_NUMBER__=987
-D__IAR_SYSTEMS_ICC__=8
-D__VA_STACK_DECREASING__=1
-D__VA_STACK_ALIGN__=2
-D__VA_STACK_ALIGN_EXTRA_BEFORE__=0
-D__LITTLE_ENDIAN__=1
-D__BOOL_TYPE__=unsigned char
-D__BOOL_SIZE__=1
-D__WCHAR_T_TYPE__=unsigned short int
-D__WCHAR_T_SIZE__=2
-D__WCHAR_T_MAX__=0xffffU
-D__DEF_PTR_MEM__=__data16
-D__DEF_PTR_SIZE__=2
-D__DATA_MEM0__=__data16
-D__DATA_MEM0_POINTER_OK__=1
-D__DATA_MEM0_UNIQUE_POINTER__=1
-D__DATA_MEM0_VAR_OK__=1
-D__DATA_MEM0_INTPTR_TYPE__=int
-D__DATA_MEM0_UINTPTR_TYPE__=unsigned int
-D__DATA_MEM0_INTPTR_SIZE_PREFIX__=""
-D__DATA_MEM0_MAX_SIZE__=0xffff
-D__DATA_MEM0_HEAP_SEGMENT__="DATA16_HEAP"
-D__DATA_MEM0_PAGE_SIZE__=0
-D__DATA_MEM0_HEAP__=0
-D__DATA_MEM1__=__regvar
-D__DATA_MEM1_POINTER_OK__=0
-D__DATA_MEM1_UNIQUE_POINTER__=0
-D__DATA_MEM1_VAR_OK__=1
-D__CODE_MEM0__=__code
-D__CODE_MEM0_POINTER_OK__=1
-D__CODE_MEM0_UNIQUE_POINTER__=1
-D__CODE_MEM0_VAR_OK__=1
-D__HEAP_MEM0__=0
-D__HEAP_DEFAULT_MEM__=0
-D__MULTIPLE_HEAPS__=1
-D__DEF_HEAP_MEM__=__data16
-D__PRAGMA_PACK_ON__=1
-D__MULTIPLE_INHERITANCE__=1
-D_CLIB_FP_INF_NAN=1
-D_DLIB_SMALL_TARGET=1
-D__430X_CORE__=1
-D__430X__=1
-D__430_CORE__=0
-D__430__=0
-D__CODE_MODEL_LARGE__=1
-D__CODE_MODEL_SMALL__=0
-D__CODE_MODEL__=__CODE_MODEL_LARGE__
-D__CORE__=__430X__
-D__DATA_MODEL_LARGE__=2
-D__DATA_MODEL_MEDIUM__=1
-D__DATA_MODEL_SMALL__=0
-D__DATA_MODEL__=__DATA_MODEL_SMALL__
-D__IAR_SYSTEMS_ICC=__IAR_SYSTEMS_ICC__
-D__ICC430__=1
-D__MULTIPLIER_16S__=2
-D__MULTIPLIER_16__=1
-D__MULTIPLIER_32__=3
-D__MULTIPLIER_NONE__=0
-D__MULTIPLIER__=__MULTIPLIER_32__
-D__REGISTER_FREE__=0
-D__REGISTER_LOCKED__=1
-D__REGISTER_MODEL_REG16__=0
-D__REGISTER_MODEL_REG20__=1
-D__REGISTER_MODEL__=__REGISTER_MODEL_REG16__
-D__REGISTER_R4__=__REGISTER_FREE__
-D__REGISTER_R5__=__REGISTER_FREE__
-D__REGISTER_REGVAR__=2
-D__PLAIN_INT_BITFIELD_IS_SIGNED__=1
-D__HAS_LOCATED_DECLARATION__=1
-D__IAR_COMPILERBASE__=595715
-D__STDC__=1
-D__STDC_VERSION__=199901L
-D__STDC_HOSTED__=1
-D__STDC_NO_VLA__=1
-D__STDC_NO_ATOMICS__=1
-D__EDG_TYPE_TRAITS_ENABLED=1
-D__EDG__=1
-D__EDG_VERSION__=410
-D__EDG_SIZE_TYPE__=unsigned int
-D__EDG_PTRDIFF_TYPE__=int
-D__EDG_JMP_BUF_ELEMENT_TYPE=unsigned short
-D__EDG_JMP_BUF_NUM_ELEMENTS=20
-D__EDG_DELTA_TYPE=int
-D__EDG_VIRTUAL_FUNCTION_INDEX_TYPE=unsigned short
-D__EDG_VAR_HANDLE_TYPE=unsigned short
-D__EDG_REGION_NUMBER_TYPE=unsigned short
-D__EDG_NULL_EH_REGION_NUMBER=65535
-D__EDG_LOWER_VARIABLE_LENGTH_ARRAYS=1
-D__EDG_ELEM_COUNT_PARAM_TYPE=long
-D__EDG_ABI_COMPATIBILITY_VERSION=9999
-D__EDG_ABI_CHANGES_FOR_RTTI=1
-D__EDG_ABI_CHANGES_FOR_ARRAY_NEW_AND_DELETE=1
-D__EDG_ABI_CHANGES_FOR_PLACEMENT_DELETE=1
-D__EDG_BSD=0
-D__EDG_SYSV=0
-D__EDG_ANSIC=1
-D__EDG_CPP11_IL_EXTENSIONS_SUPPORTED=1
-D__MSP430FR2433__=1
-D_DLIB_CONFIG_FILE_HEADER_NAME="D:\Program Files (x86)\IAR Systems\Embedded Workbench 8.0\430\lib\dlib\dl430xlsfn.h"
-D_DLIB_CONFIG_FILE_STRING="D:\\Program Files (x86)\\IAR Systems\\Embedded Workbench 8.0\\430\\lib\\dlib\\dl430xlsfn.h"
-D__VERSION__="IAR C/C++ Compiler V7.12.1.987/W32 for MSP430"
-D__CODE_MEMORY_LIST1__()=__CODE_MEM_HELPER1__(__code, 0 )
-D__CODE_MEMORY_LIST2__(_P1)=__CODE_MEM_HELPER2__(__code, 0 ,  _P1 )
-D__CODE_MEMORY_LIST3__(_P1,_P2)=__CODE_MEM_HELPER3__(__code, 0 ,  _P1 ,  _P2 )
-D__DATA_MEMORY_LIST1__()=__DATA_MEM_HELPER1__(__data, 0 )
-D__DATA_MEMORY_LIST2__(_P1)=__DATA_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__DATA_MEMORY_LIST3__(_P1,_P2)=__DATA_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__CODE_PTR_MEMORY_LIST1__()=__CODE_PTR_MEM_HELPER1__(__code, 0 )
-D__CODE_PTR_MEMORY_LIST2__(_P1)=__CODE_PTR_MEM_HELPER2__(__code, 0 ,  _P1 )
-D__CODE_PTR_MEMORY_LIST3__(_P1,_P2)=__CODE_PTR_MEM_HELPER3__(__code, 0 ,  _P1 ,  _P2 )
-D__DATA_PTR_MEMORY_LIST1__()=__DATA_PTR_MEM_HELPER1__(__data, 0 )
-D__DATA_PTR_MEMORY_LIST2__(_P1)=__DATA_PTR_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__DATA_PTR_MEMORY_LIST3__(_P1,_P2)=__DATA_PTR_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__VAR_MEMORY_LIST1__()=__VAR_MEM_HELPER1__(__data, 0 )
-D__VAR_MEMORY_LIST2__(_P1)=__VAR_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__VAR_MEMORY_LIST3__(_P1,_P2)=__VAR_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__VARD_MEMORY_LIST1__()=__VARD_MEM_HELPER1__(__data, 0, _ )
-D__HEAP_MEMORY_LIST1__()=__HEAP_MEM_HELPER1__(__data, 0 )
-D__HEAP_MEMORY_LIST2__(_P1)=__HEAP_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__HEAP_MEMORY_LIST3__(_P1,_P2)=__HEAP_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__HVAR_MEMORY_LIST1__()=__HVAR_MEM_HELPER1__(__data, 0 )
-D__HEAPD_MEMORY_LIST1__()=__HEAPD_MEM_HELPER1__(__data, 0, _ )
-D__HEAPU_MEMORY_LIST1__()=__HEAPU_MEM_HELPER1__(__data, 0 )
-D__TOPM_DATA_MEMORY_LIST1__()=
-D__TOPM_DATA_MEMORY_LIST2__(_P1)=
-D__TOPM_DATA_MEMORY_LIST3__(_P1,_P2)=
-D__TOPP_DATA_MEMORY_LIST1__()=__TOPP_DATA_MEM_HELPER1__(__data, 0 )
-D__TOPP_DATA_MEMORY_LIST2__(_P1)=__TOPP_DATA_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__TOPP_DATA_MEMORY_LIST3__(_P1,_P2)=__TOPP_DATA_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__DATA_MEM0_SIZE_TYPE__=unsigned int
-D__DATA_MEM0_INDEX_TYPE__=signed int
-D__iar_fp2bits32(x)=0
-D__iar_fp2bits64(x)=0
-D__iar_fpgethi64(x)=0
-D__iar_atomic_add_fetch(x,y,z)=0
-D__iar_atomic_sub_fetch(x,y,z)=0
-D__iar_atomic_load(x,y)=0ULL
-D__iar_atomic_compare_exchange_weak(a,b,c,d,e)=0
