#include "main.h"

unsigned int main_state=0;                                     //主循环状态
unsigned long int LED_timer_cnt=0;                             //LED定时器计数
unsigned  int state_2_led=0;





//红外BIT位接收用变量
unsigned int IR_timer_count=0;                                   //红外接收定时器计数用判断 头 0 1 
unsigned int IR_bit_state=0;                                   //红外接收 头 第几位
unsigned int IR_bit_reg=0;                                     //红外接收 暂存寄存器
unsigned int IR_receive[35]=
{0x55,0xAA,0x55,0xAA,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0}
                              ;                                 //红外接收 数据 35 包地址  实用30
unsigned int IR_receive_cunt=4;                                //红外接收 个数 计数  
unsigned int IR_bit_over=0;                                    //红外接收 是否结束
unsigned int IR_bit_check=0xFFFF;                              //红外接收 校验 
unsigned int IR_bit_check_1=0x0000;                            //红外接收 校验 
unsigned long int IR_head=0;                                   //红外接收 头0x55 0xAA 0x55 0xAA 
unsigned int IR_start_stop=0x0000;                             //红外接收 开始 结束 标识 







//unsigned int IR_bit_map=0;                                     //红外接收是否完成 标识
unsigned int stimulate_add=0;                                  //刺激器的地址
unsigned int IR_stimulate_add=0;                               //红外接收到的地址
unsigned int IR_command=0;                                     //命令

unsigned long int IR_frequency=500;                            //频率 HZ
unsigned int IR_Pulse_width=50;                                //高脉冲宽度 uS
unsigned int IR_pulse_number=5;                                //每一组的脉冲个数
unsigned int IR_crruent_1=10;                                  //电流大小uA
unsigned int IR_crruent_2=50;                                  //电流大小uA
unsigned int IR_pulse_group_number=13000;                      //脉冲刺激组数
unsigned int IR_pulse_group_interval_time=13;                  //脉冲组间隔时间 mS
unsigned int IR_channel_selection=1;                           //刺激极的选择 1为 左极 2为右极  3为双极
unsigned int IR_delay_stimulate_time=5;                        //刺激延时 S
unsigned int IR_off_on_stimulate=0;                            //刺激时红外接收是否通电
unsigned int IR_pwm_en=0;                                      //刺激是否用PWM方式 0 不用   1使用
unsigned int IR_pwm_count=50;                                   //刺激用PWM方式 高电平时间计数 1-99


unsigned int frequency_timer=0;                                 //频率 转化为 定时器 初始值   1000000/_IR_frequency      130hz
unsigned long int pulse_group_interval_time=0;                 //脉冲组间隔时间 0.1mS 转化为多少个脉冲     
unsigned long int pulse_group_number=0;                        //脉冲刺激组数
unsigned int pulse_number=0;                                   //每一组的脉冲个数
unsigned int stimulate_over=0;                                 //刺激结束标识
unsigned int stimulate_pulse_delay_state=0;                    //脉冲延时状态
unsigned int stimulate_led_state= 0;                           //刺激时LED灯的装态0 灭 1 亮
unsigned int Pulse_width=0;                                    //高脉冲宽度 uS
unsigned int channel_selection=1;                              //刺激极的选择 1为 左极 2为右极  3为双极

unsigned int timerA_cunt=0;                               //定时器A计数器
unsigned int freq_state=0;                                     //频率计时间分高电平时间和底电平时间
unsigned int group_state=0;                                    //组脉冲分为刺激时间和组等待时间

unsigned int battery_AD_count=0;                               //电池电压测量次数计数
unsigned int battery_AD_value=0;                               //电池电压测量值
unsigned int battery_AD_value_sum=0;                           //电池电压测量值 和
unsigned int battery_AD_delayed=0;                             //电池电压测量延时
unsigned int charge_AD_value_sum=0;                            //电池充电电压测量值 和
unsigned int charge_AD_value=0;                                //电池充电电压测量值  



unsigned int us_to_ms=0;                                                 //定时器A0用LED输出 主要计数器
//unsigned int timerA_cunt_ms=0;
unsigned int freq_timer0_led=0;



void LED_red_ON() {
  
    led_red_out |= led_red_bit;              
}

void LED_gree_ON() {
    led_gree_out |= led_gree_bit; 
}

void LED_red_OFF() {
    led_red_out &= ~led_red_bit;  
    
}

void LED_gree_OFF() {
    led_gree_out &= ~led_gree_bit;
    
}
 
void HallSensorSetup() {                                                       // Set-up the HALL Sensor
                                                                               // Set-up the IR
    hall_ren      |= hall_bit;                                                 // Enable internal pull-up/down resistors
    hall_out      |= hall_bit;                                                 // Select pull-up mode for P2.4
    hall_ies      |= hall_bit;                                                 // P2.4 Hi/Lo edge
    hall_ie       |= hall_bit;
    hall_ifg      &= ~hall_bit;                                                // P2.4 IFG cleared
    
    
    //hall_sel0  &= ~hall_bit;
    //hall_sel1  |= hall_bit;
   // hall_dir  |= hall_bit;
    

}


void Timer_A_con(int timeA_cunt){                                              //配置定时器 A 
  
    TA0CCTL0 |= CCIE;                                                          // CCR0 中断允许
    TA0CCTL1 |= OUTMOD_7;                                                      // CCR1 toggle/set    
    TA0CCR0 = timeA_cunt;                                                      //IR_frequency;
    TA0CCR1 = timeA_cunt;                                                      //TA0CCR0-IR_Pulse_width;
    TA0CTL = TASSEL_2 + TACLR+ ID_0+ MC_1;                                     // ACLK, upmode, 32 kHz}
             
        
}


void Timer_A_con_100us(){                                                        //配置定时器 A  1000us
  
    TA0CCTL0 |= CCIE;                                                          // CCR0 中断允许
    //TA0CCTL1 = OUTMOD_3;                                                     // CCR1 toggle/set    
    //TA0CCTL2 |= OUTMOD_3;                                                    // CCR0 中断允许
    TA0CCR0 = 105;
    //TA0CCR1 = 50;
    //TA0CCR2 = 80;
    TA0CTL = TASSEL_2 + TACLR+ ID_0+ MC_1 ;;                                   // ACLK, upmode, 32 kHz}
        

//        
}       
        
void Timer_B_con(int pwm_h){                                                   //配置定时器 B  500uS定时
  
    led_cs_dir   |=   led_cs_bit;
    led_cs_sel0  &=  ~led_cs_bit;
    led_cs_sel1  |=   led_cs_bit;
     
    TA1CCTL0 = CCIE;                                                           // TA0CCR0捕获/比较中断寄存器中断使能  
    TA1CCTL2 = OUTMOD_3;                                                       // CCR1 toggle/set
    TA1CCR0  = 104;
    //TA1CCR1 = 100;
    TA1CCR2 = pwm_h;
    TA1CTL   = TASSEL_2+TACLR+ID_0+MC_0;                          
}   

void pwm_on(){                                                                 //PWM LED_EN  输出
     TA1CTL   = TASSEL_2+TACLR+ID_0+MC_1;
}

void pwm_off(){
     TA1CTL   = TASSEL_2+TACLR+ID_0+MC_0;                                      //PWM LED_EN  关闭
}

void config_led_cs_io_out(){                                                   //配置 LED驱动芯片CS引脚为 OUT方式

    led_cs_dir   |=   led_cs_bit;
    led_cs_out   &=  ~led_cs_bit;
    led_cs_sel0  &=  ~led_cs_bit;
    led_cs_sel1  &=  ~led_cs_bit;
     
     
}     
     
void Timer_C_con(){                                                            //配置定时器 C  1000us
  

    TA2CCTL0 |= CCIE;                                                          // CCR0 中断允许
    //TA2CCTL1 = 0;                                                              // CCR1 toggle/set    
    TA2CCR0 = 525;
    //TA2CCR1 = 0;
    TA2CTL = TASSEL_2 + TACLR+ ID_0+ MC_1 ;;                                   // ACLK, upmode, 32 kHz}
               
}  

void Timer_D_con(){                                                            //配置定时器 D  1000us
  

    TA3CCTL0 |= CCIE;                                                          // CCR0 中断允许
    //TA3CCTL1 = 0;                                                              // CCR1 toggle/set   
    TA3CCR0 = 1050;
    //TA3CCR1 = 0;
    TA3CTL = TASSEL_2 + TACLR+ ID_0+ MC_0 ;;                                   // ACLK, upmode, 32 kHz}
        

//        
} 

 
void battery_AD_int(){                                                         //电池AD电压测量
  

    
    ADCCTL0 &= ~ADCENC;
    
    ADCCTL0 |= (ADCSHT_3+ ADCON);  
    ADCMCTL0 = (ADCINCH_1);  
    ADCCTL1 |= ADCSHP|ADCSSEL_0|ADCCONSEQ_2;
      
    ADCCTL0 |=  ADCENC;  
    
    ADCIE  |= ADCIE0;
     
    SYSCFG2 = ADCPCTL1;
    
      
    ADCCTL0 |= ADCSC;                                                          //ad转换
    __delay_cycles(10); 
    while(ADCCTL1 & ADCBUSY);
    battery_AD_value_sum=battery_AD_value_sum+ADCMEM0;
    
}

void charge_AD_int(){                                                          //充电AD电压测量
  

     
    ADCCTL0 &= ~ADCENC;
    
    ADCCTL0 |= (ADCSHT_3+ ADCON);  
    ADCMCTL0 = (ADCINCH_3);  
    ADCCTL1 |= ADCSHP|ADCSSEL_0|ADCCONSEQ_2;
      
    ADCCTL0 |=  ADCENC;  
    
    ADCIE  |= ADCIE0;
     
    SYSCFG2 = ADCPCTL3;
    
      
    ADCCTL0 |= ADCSC;                                                          //ad转换
    __delay_cycles(10); 
    while(ADCCTL1 & ADCBUSY);
    charge_AD_value_sum=charge_AD_value_sum+ADCMEM0;
    
}



int main( void )
{
  // Stop watchdog timer to prevent time out reset
    WDTCTL = WDTPW + WDTHOLD;
    PM5CTL0 &= ~LOCKLPM5;    
    
    
    
    P1OUT &= 0x00;                                                             // Shut down everything
    P2OUT &= 0x00;
    P3OUT &= 0x00;
    
    P1DIR &= 0x00;  
    P2DIR &= 0x00;
    P3DIR &= 0x00;   

    P1SEL0 &= 0x00;                                                            // select P1 for I/O
    P2SEL0 &= 0x00;                                                            // select P2 for I/O
    P3SEL0 &= 0x00;                                                            // select P3 for I/O
 
    P1SEL1 &= 0x00;                                                            // select P1 for I/O
    P2SEL1 &= 0x00;                                                            // select P2 for I/O
    P3SEL1 &= 0x00;   
  
    led_red_dir |= led_red_bit;
    led_red_out &= ~led_red_bit;
    
    led_gree_dir |= led_gree_bit; 
    led_gree_out &= ~led_gree_bit;
    
    ir_power_dir |= ir_power_bit;
    ir_power_out |= ir_power_bit;    
    
    //ir_re_dir |= ir_re_bit;
    //ir_re_out &= ~ir_re_bit; 
    
//    hall_dir  |= hall_bit;
//    hall_out &= ~hall_bit;   
//    hall_ren |= hall_bit;
    
    led_out1_dir |= led_out1_bit;
    led_out1_out |= led_out1_bit;  
    
    led_out2_dir |= led_out2_bit;
    led_out2_out |= led_out2_bit;  
    
    led_cs_dir |= led_cs_bit;
    led_cs_out &= ~led_cs_bit;  
    
    
    HallSensorSetup();
    IR_Setup();                                                                //配置红外引脚中断
    
    //Timer_A_con_1ms();                                                       //配置定时器 A    用来计算脉冲个数
    //Timer_B_con(10);                                                         //配置定时器 B    用来PWM输出 
    Timer_C_con();                                                             //配置定时器 C    用来计算红外 LED灯计时
    Timer_D_con();                                                           //配置定时器 D    用来计算红外停止信号
    
    _EINT();                                                                  //打开全局中断
    
    main_state=0;                                                             //主循环状态
    
    battery_AD_int();
    charge_AD_int(); 
    battery_AD_count=0;                                                        //电池电压测量次数计数
    battery_AD_value=716;                                                      //电池电压测量值   2.1V
    battery_AD_delayed=0;                                                      //电池电压测量延时    
    battery_AD_value_sum=0;                                                    //电池电压测量值 和
    __delay_cycles(100000);                                                    // Delay between transmissions 
    //_BIS_SR(LPM3_bits+GIE);
    
    while(1)
    { 
         if(main_state!=4){
              
              if(battery_AD_delayed  >=1024 )                                      //电池电压不足 低于3.3V  红LED 每间隔10秒 闪一下。电池电压大于3.7V 恢复正常工作
              {                                                                    //约 1024 约 2秒 测16次  给 battery_AD_value 更新一次
                   battery_AD_delayed=0;             
                   battery_AD_int();
                   charge_AD_int(); 
                   if(battery_AD_count == 15)
                   {
                        battery_AD_value= battery_AD_value_sum>>4;
                        charge_AD_value=charge_AD_value_sum>>4;
                        charge_AD_value_sum=0;
                        battery_AD_value_sum=0;   
                        battery_AD_count =0;       
                        if(battery_AD_value<=560)                                   //电池电压小于3.3V     3V满量程  1024最大分辨率               
                        {
                             main_state=7;
                        }
                        
                   }else{
                        battery_AD_count++; 
                   }
                   
              }else{
                   battery_AD_delayed++;
              };
         }
         
         switch (main_state){//空闲
              case(0): { 
                   main_state=1;
                   LED_timer_cnt=0;                                            //led灯用
                   ir_power_out |=  ir_power_bit;                             //开启红外电源 
                   
                   
              } break;
              case(1): { //红外接收 
                   if(IR_decode_reception()){                                  //红外 解码 接收 程序
                        main_state=2;
                        IR_start_stop=1;
                        LED_red_OFF();
                   }else{
                        main_state=1;
                   } 
                   //LED闪烁用
                   if(LED_timer_cnt>8000)  LED_timer_cnt=0;                    //500uS 为1个计数  4秒一个周期
                   if((LED_timer_cnt>1000)&&(LED_timer_cnt<1010))  LED_red_ON();
                   if(LED_timer_cnt>1010)  LED_red_OFF();
                   if(battery_AD_value>615)                                    //电池电源如果大于3.6V LED 闪2下
                   {
                        if((LED_timer_cnt>1500)&&(LED_timer_cnt<1510))  LED_red_ON();
                        if(LED_timer_cnt>1510)  LED_red_OFF();
                   }
                   if(battery_AD_value>700)                                    //电池电源如果大于4.1V LED 闪3下
                   {
                        if((LED_timer_cnt>2000)&&(LED_timer_cnt<2010))  LED_red_ON();
                        if(LED_timer_cnt>2010)  LED_red_OFF();                   
                   }
                   
                   
                   
                   
              } break;    
              case(2): { //接收完成  初始化 参数 就绪状态
                
                   IR_decode_reception();                                      //红外接收数据处理
                   if(IR_bit_over==1 ){
                     
                       IR_start_stop=1;
                       IR_bit_over=0; 
                       state_2_led=1;
                   }else;
                   
                   if(IR_start_stop==2){     
                        frequency_timer=0;                                     //频率 转化为 定时器 初始值   1000000/_IR_frequency      130hz
                        pulse_group_interval_time=0;                           //脉冲组间隔时间 0.1mS 转化为多少个脉冲     
                        pulse_group_number=1;                                  //脉冲刺激组数
                        pulse_number=0;                                        //每一组的脉冲个数
                        timerA_cunt=0;                                         //定时器A计数器
                        freq_state=0;                                          //频率计时间分高电平时间和底电平时间
                        group_state=0;                                         //组脉冲分为刺激时间和组等待时间
                        LED_red_OFF();                                         //关LED
                        LED_gree_OFF();                                        //关LED    
                        Timer_A_con_100us();                                   //配置定时器 A  100us    
                        TA2CTL = TASSEL_2+TACLR+ID_0+MC_0;                     //关定时器C中断
                        IR_timer_count=100;
                        main_state=3;  
                        if(IR_off_on_stimulate)
                        {
                             ir_power_out  |=  ir_power_bit;                    //关闭红外电源，刺激时可以不受到干扰
                        }else{
                             ir_power_out  &= ~ir_power_bit;                    //打开红外电源，刺激时可以接收控制信号
                        }
                        if(IR_channel_selection==0){                           //刺激时 两个灯都工作
                             led_out1_out |= led_out1_bit;   
                             led_out2_out |= led_out2_bit;     
                        }
                        if(IR_channel_selection==1){                           //刺激时 1个左边的灯工作
                             led_out1_out |= led_out1_bit;   
                             led_out2_out &= ~led_out2_bit;     
                        }
                        if(IR_channel_selection==2){                           //刺激时 1个灯右边的工作
                             led_out1_out &= ~led_out1_bit;   
                             led_out2_out |= led_out2_bit;     
                        }                        
                        if(IR_pwm_en)                                         //刺激是否用PWM方式 0 不用   1使用
                        {
                             Timer_B_con(IR_pwm_count);                        //配置定时器 B 
                        }else{
                             
                             config_led_cs_io_out();                           //配置 LED驱动芯片CS引脚为 OUT方式
                        } 
                        
                   }                 
                   
                   
                   //LED闪烁用
                   if(LED_timer_cnt>8000)  LED_timer_cnt=0;                     //500uS 为1个计数  4秒一个周期
                   
                   if((LED_timer_cnt>1000)&&(LED_timer_cnt<1010)) LED_gree_ON();//重新下载参数后 双灯会同时闪几下
                   if(LED_timer_cnt>1010) LED_gree_OFF();                   
                   
                   if(battery_AD_value>615)                                    //电池电源如果大于3.6V LED 闪2下
                   {
                        if((LED_timer_cnt>1500)&&(LED_timer_cnt<1510))  LED_gree_ON();
                        if(LED_timer_cnt>1510)  LED_gree_OFF();                   
                   }                  
                   
                   if(battery_AD_value>700)                                    //电池电源如果大于4.1V LED 闪3下
                   {
                        if((LED_timer_cnt>2000)&&(LED_timer_cnt<2010))  LED_gree_ON();
                        if(LED_timer_cnt>2010)  LED_gree_OFF();                   
                   }
                   
                   if((LED_timer_cnt>1500)&&(LED_timer_cnt<1510)){              //重新下载参数后 双灯会同时闪几下 
                        if(state_2_led > 0)
                        {
                             LED_red_ON();
                             state_2_led++;
                        }
                        if(state_2_led > 30) state_2_led=0;
                   }                    
                   if(LED_timer_cnt>1510) LED_red_OFF();
                   
                   
              } break;    
              case(3): {  
                   main_state=4;       
                   
              } break;    
              case(4): {                                                       //刺激开始 
                   us_to_ms=0;
                   
                   if(IR_pwm_en)                                          //刺激是否用PWM方式 0 不用   1使用
                   {
                        TA1CTL   = TASSEL_2+TACLR+ID_0+MC_1;              //开启刺激LED  PWM模式
                   }else{
                        
                        led_cs_out|=led_cs_bit;
                   } 
                   TA0CTL = TASSEL_2 + TACLR+ ID_0+ MC_1 ;                                        // 开定时器A  
                   while(main_state==4)
                   {  
                        
                        if(IR_pulse_group_number >= pulse_group_number){                           //脉冲刺激组数
                             if(group_state==0){                                                   //组脉冲刺激时间
                                  if(freq_state==0){                                               //频率计时间 高电平时间 
                                       
                                       if(IR_Pulse_width > timerA_cunt){
                                            if(IR_pwm_en)                                          //刺激是否用PWM方式 0 不用   1使用
                                            {    
                                                 led_cs_sel1  |=   led_cs_bit; 
                                                 TA1CTL   = TASSEL_2+TACLR+ID_0+MC_1;              //开启刺激LED  PWM模式
                                            }else{
                                                 
                                                 led_cs_out|=led_cs_bit;
                                            } 
                                            LED_red_ON();
                                       }else{  
                                            
                                            led_cs_sel1  &=  ~led_cs_bit;
                                            led_cs_out   &= ~led_cs_bit;
                                            
                                            if(IR_pwm_en)                                          //刺激是否用PWM方式 0 不用   1使用
                                            {
                                                TA1CTL   = TASSEL_2+TACLR+ID_0+MC_0;              //刺激LED  PWM模式  
                                                
                                            }
                                            
                                           
                                            
                                            LED_red_OFF();
                                            freq_state=1;
                                            timerA_cunt=0;
                                       }    
                                       
                                  }else{   
                                       //频率计时间 低电平时间
                                       if(IR_frequency > timerA_cunt){ 
                                            
                                       }else{
                                            freq_state=0;
                                            timerA_cunt=0;
                                            pulse_number++;
                                            if(IR_pulse_number <=pulse_number){                    //每一组的脉冲个数 
                                                 pulse_number=0;
                                                 group_state=1; 
                                            }else{ 
                                            } 
                                       }
                                       
                                  }
                                  
                                  //timerA_cunt_ms=0;
                             }else{           //组脉冲组等待时间    
                                  if(timerA_cunt>1000)
                                  {
                                       timerA_cunt=0;
                                       us_to_ms++;
                                  }  
                                  if(IR_pulse_group_interval_time < us_to_ms){                 //组间隔时间
                                       
                                       us_to_ms=0;
                                       pulse_group_number++;
                                       group_state=0;
                                       timerA_cunt=0;
                                  }else{
                                       
                                  }
                             }
                        }else{
                             
                             main_state=5;   
                             
                        }       
                        
                        
                        if(IR_start_stop==3){ 
                             
                             main_state=5;
                        }
                        
                        
                   }          
                                 
                   
                   
              } break;    
              case(5): {  //刺激结束 
                   LED_red_OFF();
                   ir_power_out |=  ir_power_bit;                              //打开红外电源，刺激时可以接收控制信号
                   config_led_cs_io_out();                                     //配置 LED驱动芯片CS引脚为 OUT方式   并 关闭刺激LED 
                   TA0CTL = TASSEL_2 + TACLR+ ID_0+ MC_0 ;                     // 关定时器A 用的时候再开
                   TA2CTL=TASSEL_2+TACLR+MC_1+ID_0;                            //开定时器C中断
                   pulse_group_number=0; 
                   IR_start_stop=1;
                   LED_timer_cnt=0;                                            //led灯用 
                   state_2_led=0;                                              //接收新的红外参数  LED指示
                   main_state=2;
              } break;    
              case(6): { //     
                   
                   main_state=6;
              } break;                           
              case(7): {                                                       //电池电压不足 低于3.3V  红LED 每间隔10秒 闪一下。电池电压大于3.7V 恢复正常工作
                   
                   
                   ir_power_out &= ~ir_power_bit;                              //关闭红外电源 
                   config_led_cs_io_out();                                     //配置 LED驱动芯片CS引脚为 OUT方式   并 关闭刺激LED 
                   TA0CTL = TASSEL_2 + TACLR+ ID_0+ MC_0 ;                     //关定时器A 用的时候再开
                   TA2CTL|= MC_1 ;                                             //开定时器C中断
                   IR_start_stop=1;
                   state_2_led=0;                                              //接收新的红外参数  LED指示
                   
                   if(battery_AD_value>631)                                    //电池电压大于3.7V
                   {
                        main_state=0;
                   }
                   //LED闪烁用 每间隔10秒 闪一下
                   if(LED_timer_cnt>20000)  LED_timer_cnt=0;                    //500uS 为1个计数  10秒一个周期
                   if((LED_timer_cnt>1000)&&(LED_timer_cnt<1020))  LED_red_ON();
                   if(LED_timer_cnt>1020)  LED_red_OFF();
                   
              } break;    
              case(8): { //刺激结束  
                   
                   main_state=0;
              } break;              
              default :{
           
                   main_state=0;
              }
         }   
    }

}
  
    
int test=0;    
int ir_state=1;                                                 //红外接收状态  1为  红外参数接收状态    2为红外 刺激开始 结束 命令  
int ir_stop_count=0;//用来消去停止的干扰
//P1.7IR   
#pragma vector=PORT1_VECTOR
__interrupt void Port_1(void) {

  if( ir_ifg & ir_re_bit){     // IR 
     
         if((4<IR_timer_count)&&( IR_timer_count <20)){                           //计时大于7.5MS  大于30MS的不作处理  
              if( 15 < IR_timer_count  )                                         //计时大于10mS 的是数据的头 握手信号
              {
                   if(IR_receive_cunt==33){
                      IR_bit_state=0;
                      IR_bit_reg=0xFF;
                      IR_bit_over=0;
                      IR_bit_check=0xFFFF;
                      IR_bit_check_1=0x0000;
                      IR_receive_cunt=4; 
                   }else{
                     IR_bit_state=0;
                     
                     
                   }
                   ir_state=2;                                                 //红外接收状态  1为  红外参数接收状态    2为红外 刺激开始 结束 命令  
              }else{
                   IR_bit_state++;
              }
              
              if((1<= IR_bit_state)&&(IR_bit_state <=8)){
                
                   IR_bit_reg=IR_bit_reg>>1;
                   if(IR_timer_count>7){                                         //  1 是 10MS  0是 14MS  
                  
                        IR_bit_reg= IR_bit_reg|0x0080; 
                   }else {
                        IR_bit_reg= IR_bit_reg&0x007f;  
                   } 
                  if(IR_bit_state ==8){
                        IR_receive[IR_receive_cunt]=IR_bit_reg;                //红外接收 个数 计数  
                        IR_receive_cunt++;  
                        IR_bit_state=0;
                   };     
              }else;  
              
              
              if(IR_receive_cunt==33){
                   for(int i=0;i<25;i++){
                        IR_bit_check_1=(IR_bit_check_1+IR_receive[i]); 
                   }
                   
                   IR_bit_check=((IR_receive[26]<<8) |IR_receive[25]);         //校验位提取  
                   if(IR_bit_check==IR_bit_check_1){
                        IR_bit_over=1;  
                   }else{
                        IR_bit_over=0;  
                   }
                   ir_state=1;                                                 //红外接收状态  1为  红外参数接收状态    2为红外 刺激开始 结束 命令  
              };  
         } 
      
         if(IR_timer_count>600){
              IR_receive_cunt=33;
              ir_state=1;                                                      //红外接收状态  1为  红外参数接收状态    2为红外 刺激开始 结束 命令  
         }
         
         if(IR_start_stop==2){
              
              TA3CTL |=  MC_1;
              test++;    
              if((ir_stop_count>3)&(ir_stop_count<8))
              {
                   test=test-2;
                   IR_start_stop=3;
                   IR_timer_count=600; 
                   TA3CTL = TASSEL_2 + TACLR+ ID_0+ MC_0 ;
                   
              } 
                           
              
              ir_stop_count=0; 
               
              
                    
         }          
         if ((ir_state == 1)&((main_state==2)&(IR_start_stop == 1))){
              if((IR_timer_count>2)&(IR_timer_count<6))
              {
                   IR_start_stop=2;
                   
              }           
                            
         } 
         IR_timer_count=0;                                                        //红外定时计数清零
         ir_ifg &= ~ir_re_bit;

    }else;

      
   
}





#pragma vector = TIMER0_A0_VECTOR
__interrupt void Timer0_A0_ISR( void )
{
    timerA_cunt++; 
} 


#pragma vector = TIMER1_A0_VECTOR
__interrupt void Timer1_A0_ISR( void )//
{

}




#pragma vector = TIMER2_A0_VECTOR
__interrupt void Timer2_A0_ISR( void )//500uS定时
{
    if(IR_timer_count < 1000)  IR_timer_count++; else;                             //红外接收用 定时计时
         
    if(LED_timer_cnt<65534) LED_timer_cnt++; else;

}

#pragma vector = TIMER3_A0_VECTOR
__interrupt void Timer3_A0_ISR( void )                                         //800uS定时    高  低  高 低  高
{
     
     if(ir_stop_count > 30)
     {
          TA3CTL = TASSEL_2 + TACLR+ ID_0+ MC_0 ;                   // 关闭定时器D 
          ir_stop_count=0;
          
     }else{
          
          ir_stop_count++;   
          
     }
     
     
}






#pragma vector=ADC_VECTOR  
__interrupt void ADC_ISR(void) {

    ADCCTL0 &= ~ADCENC;      //disable ADC
    //ADCCTL0 &= ~(REFON + ADCON + ADCIE); // and then shutdown completely
    
    ADCIFG &= ~ADCIFG0;
    
}

  
    
  










