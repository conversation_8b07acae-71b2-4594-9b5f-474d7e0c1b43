#ifndef main_H_
#define main_H_
#include <msp430FR2433.h>
#include "ir.h"
#include "AD.h"



//LED灯
#define led_red_bit  BIT0                                       // LED灯 红色
#define led_red_out  P2OUT
#define led_red_dir  P2DIR

#define led_gree_bit  BIT5                                      // LED灯 绿色
#define led_gree_out  P1OUT
#define led_gree_dir  P1DIR

#define led_out1_bit  BIT2                                       // 刺激 LED灯 1
#define led_out1_out  P3OUT
#define led_out1_dir  P3DIR

#define led_out2_bit  BIT7                                      // 刺激 LED灯 2
#define led_out2_out  P2OUT
#define led_out2_dir  P2DIR

#define led_cs_bit  BIT4                                      // 刺激 LED灯 CS
#define led_cs_out  P1OUT
#define led_cs_dir  P1DIR
#define led_cs_sel0 P1SEL0  
#define led_cs_sel1 P1SEL1  


#define ir_power_bit  BIT1                                      // 红外接收  电源接地
#define ir_power_out  P2OUT
#define ir_power_dir  P2DIR

#define ir_re_bit  BIT6                                         // 红外接收
#define ir_re_out  P1OUT
#define ir_re_dir  P1DIR
#define ir_ren     P1REN                                      
#define ir_ies     P1IES
#define ir_ie      P1IE
#define ir_ifg     P1IFG
#define ir_in      P1IN

#define hall_bit  BIT7                                         // 磁控开关引脚
#define hall_out  P1OUT
#define hall_dir  P1DIR

#define hall_ren  P1REN 
#define hall_ies  P1IES
#define hall_ie   P1IE
#define hall_ifg  P1IFG
#define hall_sel0 P1SEL0  
#define hall_sel1 P1SEL1  
#define hall_dir  P1DIR













#define ad1_bit  BIT1                                         // 电池电量 AD1输入引脚
#define ad1_out  P1OUT
#define ad1_dir  P1DIR

#define ad2_bit  BIT3                                         // 充电 AD2输入引脚
#define ad2_out  P1OUT
#define ad2_dir  P1DIR








//红外BIT位接收用变量
extern unsigned int IR_timer_cnt;                                    //红外接收定时器计数用判断 头 0 1 
extern unsigned int IR_bit_state;                                    //红外接收 头 第几位
extern unsigned int IR_bit_reg;                                      //红外接收 暂存寄存器
extern unsigned int IR_receive[35];                                  //红外接收 数据 35 包地址  实用30
extern unsigned int IR_receive_cunt;                                 //红外接收 个数 计数  
extern unsigned int IR_bit_over;                                     //红外接收 是否结束
extern unsigned int IR_bit_check;                                    //红外接收 校验 
extern unsigned int IR_bit_check_1;                                  //红外接收 校验 
extern unsigned long int IR_head;                                    //红外接收 头0x55 0xAA 0x55 0xAA 



extern unsigned int stimulate_add;                                   //刺激器的地址
extern unsigned int IR_stimulate_add;                                //红外接收到的地址
extern unsigned int IR_command;                                      //命令

extern unsigned long int IR_frequency;                               //频率 HZ
extern unsigned int IR_Pulse_width;                                  //高脉冲宽度 uS
extern unsigned int IR_pulse_number;                                 //每一组的脉冲个数
extern unsigned int IR_crruent_1;                                    //电流大小uA
extern unsigned int IR_crruent_2;                                    //电流大小uA
extern unsigned int IR_pulse_group_number;                           //脉冲刺激组数
extern unsigned int IR_pulse_group_interval_time;                    //脉冲组间隔时间 mS
extern unsigned int IR_channel_selection;                            //刺激极的选择 1为 左极 2为右极  3为双极
extern unsigned int IR_delay_stimulate_time;                         //刺激延时 S
extern unsigned int IR_off_on_stimulate;                             //刺激时红外接收是否通电
extern unsigned int IR_pwm_en;                                       //刺激是否用PWM方式 0 不用   1使用
extern unsigned int IR_pwm_count;                                    //刺激用PWM方式 高电平时间计数 1-99


#endif
